// Website Cloning Script
// Run this in the browser console on the target website

function cloneWebsite() {
    // Get the complete HTML
    const html = document.documentElement.outerHTML;
    
    // Get all CSS styles
    const styles = Array.from(document.styleSheets).map(sheet => {
        try {
            return Array.from(sheet.cssRules).map(rule => rule.cssText).join('\n');
        } catch (e) {
            return '';
        }
    }).join('\n');
    
    // Get all JavaScript
    const scripts = Array.from(document.scripts).map(script => {
        if (script.src) {
            return `// External script: ${script.src}`;
        }
        return script.innerHTML;
    }).join('\n\n');
    
    // Create downloadable files
    downloadFile('index.html', html);
    downloadFile('styles.css', styles);
    downloadFile('scripts.js', scripts);
    
    console.log('Website cloned! Check your downloads folder.');
}

function downloadFile(filename, content) {
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// Run the cloning function
cloneWebsite();
