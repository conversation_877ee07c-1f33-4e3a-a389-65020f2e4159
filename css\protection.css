/* Placeholder for protection.css. Add your styles here. */
/* CSS Protection và Anti-Debugging */

/* <PERSON>ống select và copy nội dung */
* {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
}

/* <PERSON> phép select trong input và textarea */
input,
textarea,
[contenteditable="true"] {
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
}

/* <PERSON><PERSON><PERSON> vệ hình ảnh */
img {
    pointer-events: none;
    -webkit-user-drag: none;
    -moz-user-drag: none;
    -ms-user-drag: none;
    user-drag: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Ẩn scrollbar để khó inspect */
::-webkit-scrollbar {
    width: 0px;
    background: transparent;
}

/* <PERSON><PERSON><PERSON> inspect element */
*::selection {
    background: transparent;
}

*::-moz-selection {
    background: transparent;
}

/* <PERSON><PERSON><PERSON><PERSON> khi có người cố gắng inspect */
.protection-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(45deg, #ff6b9d, #4ecdc4);
    z-index: 999999;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-family: Arial, sans-serif;
    font-size: 24px;
    text-align: center;
    animation: protectionPulse 2s infinite;
}

@keyframes protectionPulse {
    0% {
        opacity: 0.8;
    }

    50% {
        opacity: 1;
    }

    100% {
        opacity: 0.8;
    }
}

/* Ẩn source code trong view-source */
@media print {
    * {
        display: none !important;
    }

    body::after {
        content: "🔒 Trang web được bảo vệ - Không thể in";
        display: block !important;
        font-size: 24px;
        text-align: center;
        padding: 50px;
        color: red;
    }
}

/* Chống view source */
@media screen {
    body {
        font-family: Arial, sans-serif;
    }
}

/* Bảo vệ chống devtools */
@media (max-width: 0px) {
    * {
        display: none !important;
    }
}

/* Làm rối code CSS */
.obfuscated {
    position: absolute;
    left: -9999px;
    top: -9999px;
    opacity: 0;
    pointer-events: none;
}

/* Tạo nhiễu cho inspector */
.noise-element {
    position: fixed;
    width: 1px;
    height: 1px;
    opacity: 0.001;
    pointer-events: none;
    z-index: -1;
}

/* Cảnh báo khi mở devtools */
.devtools-warning {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #ff4444;
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    z-index: 99999;
    font-weight: bold;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    animation: slideInWarning 0.3s ease-out;
}

@keyframes slideInWarning {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Chống copy qua CSS */
.no-copy {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    -webkit-touch-callout: none !important;
    -webkit-tap-highlight-color: transparent !important;
}

/* Bảo vệ form */
form {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

form input,
form textarea,
form select {
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
}

/* Ẩn cursor khi hover vào element được bảo vệ */
.protected-element {
    cursor: default !important;
}

/* Tạo overlay bảo vệ toàn trang */
.full-protection-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 999999;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    text-align: center;
    backdrop-filter: blur(10px);
}

/* Hiệu ứng loading khi phát hiện devtools */
.protection-loading {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #ff6b9d;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Tạo nhiễu elements */
.noise-1::before,
.noise-2::after,
.noise-3::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    opacity: 0;
}

/* Chống highlight */
::selection {
    background: transparent;
}

::-moz-selection {
    background: transparent;
}

/* Bảo vệ links */
a {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Hide payment modal */
#paymentModal {
    display: none !important;
}