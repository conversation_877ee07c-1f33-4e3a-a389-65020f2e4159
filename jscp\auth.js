// Authentication Management for Birthday Website
class AuthManager {
    constructor() {
        this.user = null;
        this.isAuthenticated = false;
        this.firebaseConfig = {
            // Add your Firebase config here if needed
            // For demo purposes, we'll use a mock authentication
        };
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.checkAuthState();
        this.initializeGoogleAuth();
    }

    setupEventListeners() {
        const loginBtn = document.getElementById('googleLoginBtn');
        const logoutBtn = document.getElementById('logoutBtn');

        if (loginBtn) {
            loginBtn.addEventListener('click', () => {
                this.signInWithGoogle();
            });
        }

        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                this.signOut();
            });
        }
    }

    async initializeGoogleAuth() {
        // For demo purposes, we'll simulate Google Auth
        // In a real implementation, you would initialize Firebase Auth here
        console.log('Google Auth initialized (demo mode)');
    }

    async signInWithGoogle() {
        try {
            // Show loading
            const loginBtn = document.getElementById('googleLoginBtn');
            if (loginBtn) {
                loginBtn.style.opacity = '0.6';
                loginBtn.style.pointerEvents = 'none';
                loginBtn.innerHTML = `
                    <svg width="18" height="18" viewBox="0 0 24 24">
                        <circle cx="12" cy="12" r="3" fill="currentColor">
                            <animate attributeName="r" values="3;5;3" dur="1s" repeatCount="indefinite"/>
                        </circle>
                    </svg>
                    <span style="margin-left: 8px;">Signing in...</span>
                `;
            }

            // Simulate authentication delay
            await new Promise(resolve => setTimeout(resolve, 1500));

            // For demo purposes, create a mock user
            const mockUser = {
                uid: 'demo-user-' + Date.now(),
                displayName: 'Demo User',
                email: '<EMAIL>',
                photoURL: 'https://via.placeholder.com/40x40/ff6b9d/white?text=DU'
            };

            this.handleAuthSuccess(mockUser);
            
        } catch (error) {
            console.error('Authentication failed:', error);
            this.handleAuthError(error);
        }
    }

    handleAuthSuccess(user) {
        this.user = user;
        this.isAuthenticated = true;
        
        // Store user data
        localStorage.setItem('authUser', JSON.stringify(user));
        
        // Update UI
        this.updateAuthUI();
        
        // Show success notification
        if (window.uiManager) {
            window.uiManager.showNotification(
                `Welcome, ${user.displayName || 'User'}!`, 
                'success'
            );
        }
    }

    handleAuthError(error) {
        console.error('Auth error:', error);
        
        // Reset login button
        const loginBtn = document.getElementById('googleLoginBtn');
        if (loginBtn) {
            loginBtn.style.opacity = '1';
            loginBtn.style.pointerEvents = 'auto';
            loginBtn.innerHTML = `
                <svg width="18" height="18" viewBox="0 0 24 24">
                    <path fill="#4285f4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34a853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#fbbc05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#ea4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
            `;
        }
        
        // Show error notification
        if (window.uiManager) {
            window.uiManager.showNotification(
                'Authentication failed. Please try again.', 
                'error'
            );
        }
    }

    async signOut() {
        try {
            // Clear user data
            this.user = null;
            this.isAuthenticated = false;
            localStorage.removeItem('authUser');
            
            // Update UI
            this.updateAuthUI();
            
            // Show success notification
            if (window.uiManager) {
                window.uiManager.showNotification('Signed out successfully', 'success');
            }
            
        } catch (error) {
            console.error('Sign out failed:', error);
            if (window.uiManager) {
                window.uiManager.showNotification('Sign out failed', 'error');
            }
        }
    }

    checkAuthState() {
        // Check if user is stored in localStorage
        const storedUser = localStorage.getItem('authUser');
        if (storedUser) {
            try {
                this.user = JSON.parse(storedUser);
                this.isAuthenticated = true;
                this.updateAuthUI();
            } catch (error) {
                console.error('Failed to parse stored user:', error);
                localStorage.removeItem('authUser');
            }
        }
    }

    updateAuthUI() {
        const loginBtn = document.getElementById('googleLoginBtn');
        const userInfo = document.getElementById('userInfo');
        const userName = document.getElementById('userName');
        const userEmail = document.getElementById('userEmail');
        const userAvatar = document.getElementById('userAvatar');

        if (this.isAuthenticated && this.user) {
            // Hide login button
            if (loginBtn) {
                loginBtn.style.display = 'none';
            }
            
            // Show user info
            if (userInfo) {
                userInfo.style.display = 'flex';
            }
            
            // Update user details
            if (userName) {
                userName.textContent = this.user.displayName || 'User';
            }
            
            if (userEmail) {
                userEmail.textContent = this.user.email || '';
            }
            
            if (userAvatar) {
                userAvatar.src = this.user.photoURL || 'https://via.placeholder.com/40x40/ff6b9d/white?text=' + 
                    (this.user.displayName ? this.user.displayName.charAt(0).toUpperCase() : 'U');
                userAvatar.alt = this.user.displayName || 'User Avatar';
            }
        } else {
            // Show login button
            if (loginBtn) {
                loginBtn.style.display = 'flex';
            }
            
            // Hide user info
            if (userInfo) {
                userInfo.style.display = 'none';
            }
        }
    }

    getCurrentUser() {
        return this.user;
    }

    isUserAuthenticated() {
        return this.isAuthenticated;
    }

    getUserId() {
        return this.user ? this.user.uid : null;
    }

    getUserEmail() {
        return this.user ? this.user.email : null;
    }

    getUserDisplayName() {
        return this.user ? this.user.displayName : null;
    }

    // Method to check if user has permission for certain actions
    hasPermission(action) {
        if (!this.isAuthenticated) {
            return false;
        }

        // Define permissions based on user or action type
        const permissions = {
            'create_website': true,
            'save_settings': true,
            'upload_images': true,
            'access_premium': false // Could be based on user subscription
        };

        return permissions[action] || false;
    }

    // Method to require authentication for certain actions
    requireAuth(callback, errorMessage = 'Please sign in to continue') {
        if (this.isAuthenticated) {
            callback();
        } else {
            if (window.uiManager) {
                window.uiManager.showNotification(errorMessage, 'warning');
            }
            // Optionally trigger sign-in flow
            this.signInWithGoogle();
        }
    }

    // Method to get auth headers for API calls
    getAuthHeaders() {
        if (this.isAuthenticated && this.user) {
            return {
                'Authorization': `Bearer ${this.user.uid}`, // In real app, use proper token
                'X-User-ID': this.user.uid
            };
        }
        return {};
    }

    // Method to handle auth state changes
    onAuthStateChanged(callback) {
        // In a real implementation, this would listen to Firebase auth state changes
        // For demo, we'll just call the callback with current state
        if (typeof callback === 'function') {
            callback(this.user);
        }
    }
}

// Initialize auth manager
document.addEventListener('DOMContentLoaded', () => {
    window.authManager = new AuthManager();
});
