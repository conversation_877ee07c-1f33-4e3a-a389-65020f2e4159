// Placeholder for voucher.obf.js
const a0_0x4a66e6 = a0_0x5576;
(function(_0xf3ea74, _0x91684c) {
    const _0xf58bb1 = a0_0x5576
      , _0x27d5e7 = _0xf3ea74();
    while (!![]) {
        try {
            const _0x2594b5 = parseInt(_0xf58bb1(0x163)) / 0x1 + parseInt(_0xf58bb1(0x17b)) / 0x2 + -parseInt(_0xf58bb1(0x17e)) / 0x3 + parseInt(_0xf58bb1(0x164)) / 0x4 * (-parseInt(_0xf58bb1(0x189)) / 0x5) + parseInt(_0xf58bb1(0x165)) / 0x6 + -parseInt(_0xf58bb1(0x184)) / 0x7 + -parseInt(_0xf58bb1(0x193)) / 0x8 * (parseInt(_0xf58bb1(0x18b)) / 0x9);
            if (_0x2594b5 === _0x91684c)
                break;
            else
                _0x27d5e7['push'](_0x27d5e7['shift']());
        } catch (_0x2806ec) {
            _0x27d5e7['push'](_0x27d5e7['shift']());
        }
    }
}(a0_0x3a3f, 0x69af7));
const voucherList = document[a0_0x4a66e6(0x192)](a0_0x4a66e6(0x176))
  , voucherResult = document['getElementById'](a0_0x4a66e6(0x18c))
  , totalPriceDiv = document[a0_0x4a66e6(0x192)](a0_0x4a66e6(0x183));
function a0_0x3a3f() {
    const _0x585922 = ['from', '\x20(-', 'voucherList', 'expiredAt', 'updatePricing', 'message', 'https://dearlove-backend.onrender.com/api/vouchers/saved/', '588736DtNjRr', 'discountValue', 'toLocaleString', '1461696eWGxKd', '\x22\x20style=\x22cursor:\x20pointer;\x20font-size:\x2011px;\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<strong>', 'change', 'round', 'user_uid', 'totalPrice', '183638fsmbID', 'length', 'voucher', 'display', 'getItem', '4110315htZMtM', '\x20VNĐ</span>\x20<span\x20style=\x22font-size:14px;color:#6c63ff;\x22>(đã\x20áp\x20dụng\x20voucher)</span>', '1446129LkUSDR', 'voucherResult', 'map', 'toLocaleDateString', 'input[name=\x22voucher\x22]', 'innerHTML', '\x22\x20style=\x22margin-right:\x208px;\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20<label\x20for=\x22voucher_', 'getElementById', '8goVuLd', 'success', 'style', 'pricingCalculator', 'Tổng\x20tiền\x20thanh\x20toán:\x20<span\x20style=\x22color:#6c63ff;\x22>', 'forEach', 'data', '<span\x20style=\x22color:#e53935;\x22>Không\x20thể\x20tải\x20voucher!</span>', '\x20VNĐ</span>', '</strong>\x20-\x20Giảm:\x20', 'Error\x20loading\x20vouchers:', 'DOMContentLoaded', 'checked', '792021vNLJlz', '4oelJFt', '5056248XnYvho', 'join', 'target', '\x22\x20style=\x22\x0a\x20\x20\x20\x20\x20\x20\x20\x20padding:\x206px\x208px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20margin:\x204px\x200;\x0a\x20\x20\x20\x20\x20\x20\x20\x20border:\x201px\x20solid\x20rgba(255,255,255,0.3);\x0a\x20\x20\x20\x20\x20\x20\x20\x20border-radius:\x204px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20cursor:\x20pointer;\x0a\x20\x20\x20\x20\x20\x20\x20\x20transition:\x20all\x200.3s\x20ease;\x0a\x20\x20\x20\x20\x20\x20\x22\x20onmouseover=\x22this.style.background=\x27rgba(255,255,255,0.1)\x27\x22\x20onmouseout=\x22this.style.background=\x27transparent\x27\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20<input\x20class=\x22checkbox\x22\x20type=\x22checkbox\x22\x20name=\x22voucher\x22\x20id=\x22voucher_', 'code', 'error', 'none', 'voucherManager', 'block', 'name', 'addEventListener', 'querySelectorAll', '</span>\x0a\x20\x20\x20\x20\x20\x20\x20\x20</label>\x0a\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20', '<span\x20style=\x22color:#e53935;\x22>', '<span\x20style=\x22color:#e53935;\x22>Bạn\x20cần\x20đăng\x20nhập\x20để\x20xem\x20voucher!</span>'];
    a0_0x3a3f = function() {
        return _0x585922;
    }
    ;
    return a0_0x3a3f();
}
let selectedVoucher = null
  , vouchers = [];
function a0_0x5576(_0x1c12a2, _0x216891) {
    const _0x3a3ff8 = a0_0x3a3f();
    return a0_0x5576 = function(_0x5576f4, _0x340679) {
        _0x5576f4 = _0x5576f4 - 0x159;
        let _0x1fe711 = _0x3a3ff8[_0x5576f4];
        return _0x1fe711;
    }
    ,
    a0_0x5576(_0x1c12a2, _0x216891);
}
function updateTotalPrice() {
    const _0x10e92b = a0_0x4a66e6;
    if (window[_0x10e92b(0x159)]) {
        window['pricingCalculator'][_0x10e92b(0x178)]();
        return;
    }
    const _0x46238e = document[_0x10e92b(0x192)](_0x10e92b(0x183));
    if (!_0x46238e)
        return;
    let _0x30ff0d = window['ORIGINAL_PRICE'] || 0x0;
    selectedVoucher ? (_0x30ff0d = _0x30ff0d - Math[_0x10e92b(0x181)](_0x30ff0d * selectedVoucher['discountValue'] / 0x64),
    _0x46238e[_0x10e92b(0x190)] = _0x10e92b(0x15a) + _0x30ff0d['toLocaleString']() + _0x10e92b(0x18a)) : _0x46238e[_0x10e92b(0x190)] = _0x10e92b(0x15a) + _0x30ff0d[_0x10e92b(0x17d)]() + _0x10e92b(0x15e);
}
async function loadUserVouchers() {
    const _0x44f85a = a0_0x4a66e6;
    if (!voucherList)
        return;
    voucherList[_0x44f85a(0x190)] = 'Đang\x20tải\x20voucher...',
    voucherResult[_0x44f85a(0x195)][_0x44f85a(0x187)] = _0x44f85a(0x16b),
    selectedVoucher = null,
    vouchers = [],
    updateTotalPrice();
    const _0x4001bf = localStorage[_0x44f85a(0x188)](_0x44f85a(0x182));
    if (!_0x4001bf) {
        voucherList[_0x44f85a(0x190)] = _0x44f85a(0x173);
        return;
    }
    try {
        const _0x2e3c55 = await fetch(_0x44f85a(0x17a) + _0x4001bf)
          , _0x555fd7 = await _0x2e3c55['json']();
        if (!_0x555fd7[_0x44f85a(0x194)]) {
            voucherList['innerHTML'] = _0x44f85a(0x172) + _0x555fd7[_0x44f85a(0x179)] + '</span>';
            return;
        }
        if (!_0x555fd7[_0x44f85a(0x15c)][_0x44f85a(0x185)]) {
            voucherList['innerHTML'] = '<span\x20style=\x22color:#888;\x22>Bạn\x20không\x20có\x20voucher\x20nào\x20cả!</span>';
            return;
        }
        vouchers = _0x555fd7[_0x44f85a(0x15c)],
        voucherList[_0x44f85a(0x190)] = vouchers[_0x44f85a(0x18d)]( (_0x4d8441, _0x32e674) => '\x0a\x20\x20\x20\x20\x20\x20<div\x20class=\x22voucher-item\x22\x20data-idx=\x22' + _0x32e674 + _0x44f85a(0x168) + _0x32e674 + _0x44f85a(0x191) + _0x32e674 + _0x44f85a(0x17f) + _0x4d8441[_0x44f85a(0x169)] + _0x44f85a(0x15f) + _0x4d8441['discountValue'] + '%<br>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<span\x20style=\x22color:\x20#ccc;\x22>HSD:\x20' + new Date(_0x4d8441[_0x44f85a(0x177)])[_0x44f85a(0x18e)]() + _0x44f85a(0x171))[_0x44f85a(0x166)](''),
        selectedVoucher = null,
        updateTotalPrice();
    } catch (_0x12dd43) {
        console[_0x44f85a(0x16a)](_0x44f85a(0x160), _0x12dd43),
        voucherList[_0x44f85a(0x190)] = _0x44f85a(0x15d);
    }
}
voucherList && voucherList[a0_0x4a66e6(0x16f)](a0_0x4a66e6(0x180), _0x221a7d => {
    const _0x2ab3a9 = a0_0x4a66e6;
    if (_0x221a7d['target'][_0x2ab3a9(0x16e)] === _0x2ab3a9(0x186)) {
        const _0x5348ea = voucherList[_0x2ab3a9(0x170)](_0x2ab3a9(0x18f))
          , _0x3c3bb3 = Array[_0x2ab3a9(0x174)](_0x5348ea)['findIndex'](_0x1739a3 => _0x1739a3 === _0x221a7d[_0x2ab3a9(0x167)]);
        _0x221a7d[_0x2ab3a9(0x167)][_0x2ab3a9(0x162)] ? (_0x5348ea[_0x2ab3a9(0x15b)]( (_0x36304d, _0x51fe98) => _0x36304d[_0x2ab3a9(0x162)] = _0x51fe98 === _0x3c3bb3),
        selectedVoucher = vouchers[_0x3c3bb3],
        voucherResult && (voucherResult['innerHTML'] = '✅\x20Đã\x20áp\x20dụng\x20voucher:\x20' + selectedVoucher['code'] + _0x2ab3a9(0x175) + selectedVoucher[_0x2ab3a9(0x17c)] + '%)',
        voucherResult[_0x2ab3a9(0x195)][_0x2ab3a9(0x187)] = _0x2ab3a9(0x16d))) : (selectedVoucher = null,
        voucherResult && (voucherResult[_0x2ab3a9(0x195)][_0x2ab3a9(0x187)] = 'none')),
        updateTotalPrice();
    }
}
);
document[a0_0x4a66e6(0x16f)](a0_0x4a66e6(0x161), loadUserVouchers),
window[a0_0x4a66e6(0x16c)] = {
    'loadUserVouchers': loadUserVouchers,
    'selectedVoucher': () => selectedVoucher,
    'vouchers': () => vouchers
};
