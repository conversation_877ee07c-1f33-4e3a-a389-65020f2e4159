class BirthdayAPI {
  constructor() {
    this.baseURL = 'https://dearlove-backend.onrender.com/api/birthday';
    this.currentWebsiteId = null;
  }

  /**
   * Upload an image (base64 or blob) to R2 bucket and return its URL.
   */
  async uploadImageToR2(base64Image, prefix = 'birthday') {
    // Convert base64 to File
    const file = this.base64ToFile(base64Image, 'upload.png');
    const formData = new FormData();
    formData.append('file', file);
    formData.append('prefix', prefix);

    const response = await fetch('https://dearlove-backend.onrender.com/api/r2/upload', {
      method: 'POST',
      body: formData
    });

    const data = await response.json();
    if (data.success && data.data && data.data.url) {
      return data.data.url;
    } else {
      throw new Error('Upload failed');
    }
  }

  /**
   * Convert a base64 string to a File object.
   */
  base64ToFile(base64, filename) {
    const arr = base64.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, { type: mime });
  }

  /**
   * Read a File to base64.
   */
  fileToBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  /**
   * Process uploaded pages' images.
   */
  async processFileInputImages(pages) {
    const updatedPages = [];
    for (let i = 0; i < pages.length; i++) {
      const page = { ...pages[i] };
      const input = document.getElementById(`pageImage${i}`);
      if (input && input.files.length > 0) {
        const file = input.files[0];
        const base64 = await this.fileToBase64(file);
        const url = await this.uploadImageToR2(base64);
        page.image = url;
      } else if (page.image && (page.image.startsWith('blob:') || page.image.startsWith('data:'))) {
        const url = await this.uploadImageToR2(page.image);
        page.image = url;
      }
      updatedPages.push(page);
    }
    return updatedPages;
  }

  /**
   * Upload page images by fetching blob.
   */
  async uploadPagesImages(pages) {
    const updatedPages = [];
    for (const page of pages) {
      const updatedPage = { ...page };
      if (page.image) {
        if (page.image.startsWith('blob:')) {
          const blob = await fetch(page.image).then(res => res.blob());
          const base64 = await this.fileToBase64(blob);
          updatedPage.image = await this.uploadImageToR2(base64);
        } else if (page.image.startsWith('data:')) {
          updatedPage.image = await this.uploadImageToR2(page.image);
        }
      }
      updatedPages.push(updatedPage);
    }
    return updatedPages;
  }

  /**
   * Create new birthday website.
   */
  async createBirthdayWebsite(settings, status = 'Free') {
    if (!settings || typeof settings !== 'object') {
      throw new Error('Settings must be an object.');
    }

    const newSettings = { ...settings };

    if (newSettings.enableBook && newSettings.pages && newSettings.pages.length > 0) {
      try {
        const pagesWithUploadedImages = await this.processFileInputImages(newSettings.pages);
        const pagesWithUploadedBlobs = await this.uploadPagesImages(pagesWithUploadedImages);
        newSettings.pages = pagesWithUploadedBlobs;
      } catch (err) {
        console.error('❌ Error uploading page images:', err);
        throw new Error('Failed to upload page images: ' + err.message);
      }
    }

    const response = await fetch(this.baseURL + '/birthday-websites', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        settings: newSettings,
        status: status
      })
    });

    if (!response.ok) {
      const text = await response.text();
      throw new Error(`HTTP error! ${response.status}: ${text}`);
    }

    const data = await response.json();
    if (data.success) {
      this.currentWebsiteId = data.data.websiteId;
      localStorage.setItem('currentWebsiteId', this.currentWebsiteId);
      return {
        success: true,
        data: data.data,
        websiteId: this.currentWebsiteId
      };
    } else {
      throw new Error(data.message || 'Could not create website');
    }
  }

  /**
   * Get website by websiteId.
   */
  async getBirthdayWebsiteByWebsiteId(websiteId) {
    const response = await fetch(this.baseURL + '/birthday-websites/website/' + websiteId);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    if (data.success) {
      return { success: true, data: data.data };
    } else {
      throw new Error(data.message || 'Could not find website');
    }
  }

  /**
   * Make shareable URL.
   */
  createShareableURL(websiteId) {
    return `${window.location.origin}${window.location.pathname}?websiteId=${websiteId}`;
  }

  /**
   * Get websiteId from URL.
   */
  getWebsiteIdFromURL() {
    const params = new URLSearchParams(window.location.search);
    return params.get('websiteId');
  }

  /**
   * Get websiteId from local storage.
   */
  getCurrentWebsiteId() {
    return this.currentWebsiteId || localStorage.getItem('currentWebsiteId');
  }

  /**
   * Clear websiteId from local storage.
   */
  clearWebsiteId() {
    localStorage.removeItem('currentWebsiteId');
    this.currentWebsiteId = null;
  }
}

// Export or attach to window
window.birthdayAPI = new BirthdayAPI();
