// Placeholder for auth.obf.js
function a0_0x2c1a() {
    const _0x3e6d4b = ['apps', 'deargift-e6488.appspot.com', 'user_uid', 'getIdToken', 'none', 'email', '<div\x20style=\x22display:\x20inline-block;\x20width:\x2012px;\x20height:\x2012px;\x20border:\x202px\x20solid\x20#4285f4;\x20border-radius:\x2050%;\x20border-top-color:\x20transparent;\x20animation:\x20spin\x201s\x20linear\x20infinite;\x22></div>\x20Đang\x20đăng\x20nhập...', 'GoogleAuthProvider', 'auth', 'addEventListener', 'photoURL', '16341560FrFMBb', 'head', '24233uQnFcO', 'appendChild', 'onAuthStateChanged', 'uid', 'userAvatar', '3NUwVbx', 'userInfo', 'user', 'googleLoginBtn', 'logoutBtn', 'G-VGBLRMQM9L', '3491932iyXYiL', 'getElementById', 'setItem', 'flex', 'innerHTML', '371YNygUb', 'user_email', 'message', '168145XgDQlK', 'error', '************', 'displayName', 'signInWithPopup', '167848eCNIzF', '\x0a\x20\x20@keyframes\x20spin\x20{\x0a\x20\x20\x20\x200%\x20{\x20transform:\x20rotate(0deg);\x20}\x0a\x20\x20\x20\x20100%\x20{\x20transform:\x20rotate(360deg);\x20}\x0a\x20\x20}\x0a\x20\x20\x0a\x20\x20#googleLoginBtn:hover\x20{\x0a\x20\x20\x20\x20background:\x20#f8f9fa\x20!important;\x0a\x20\x20\x20\x20box-shadow:\x200\x204px\x208px\x20rgba(0,0,0,0.15)\x20!important;\x0a\x20\x20}\x0a\x20\x20\x0a\x20\x20#googleLoginBtn:disabled\x20{\x0a\x20\x20\x20\x20opacity:\x200.6;\x0a\x20\x20\x20\x20cursor:\x20not-allowed;\x0a\x20\x20}\x0a', 'disabled', 'removeItem', '❌\x20Logout\x20failed:', '12234069DSRIkq', '132lhZhBn', '❌\x20Login\x20failed:', 'length', 'src', '4ihuwGq', 'https://via.placeholder.com/24x24/4285f4/ffffff?text=👤', 'userName', '❌\x20Đăng\x20xuất\x20thất\x20bại:\x20', 'style', '1:************:web:4b1c98f17f824690e2e7be', '❌\x20Đăng\x20nhập\x20thất\x20bại:\x20', 'display', 'POST', 'AIzaSyDN3YxOpqxqfBQgHOvtFk6JJvztlV3vTH8', '42IJfdFy', '850372ikHpNA', 'pricingCalculator', 'textContent'];
    a0_0x2c1a = function() {
        return _0x3e6d4b;
    }
    ;
    return a0_0x2c1a();
}
const a0_0x3e3a54 = a0_0x17aa;
(function(_0x8a5f86, _0x3f17f8) {
    const _0x1ea0e7 = a0_0x17aa
      , _0x5b7ee7 = _0x8a5f86();
    while (!![]) {
        try {
            const _0xbbbbac = parseInt(_0x1ea0e7(0xdf)) / 0x1 * (parseInt(_0x1ea0e7(0xd4)) / 0x2) + parseInt(_0x1ea0e7(0xb7)) / 0x3 * (parseInt(_0x1ea0e7(0xbd)) / 0x4) + parseInt(_0x1ea0e7(0xc5)) / 0x5 * (parseInt(_0x1ea0e7(0xde)) / 0x6) + -parseInt(_0x1ea0e7(0xc2)) / 0x7 * (-parseInt(_0x1ea0e7(0xca)) / 0x8) + -parseInt(_0x1ea0e7(0xcf)) / 0x9 + -parseInt(_0x1ea0e7(0xb0)) / 0xa + parseInt(_0x1ea0e7(0xb2)) / 0xb * (-parseInt(_0x1ea0e7(0xd0)) / 0xc);
            if (_0xbbbbac === _0x3f17f8)
                break;
            else
                _0x5b7ee7['push'](_0x5b7ee7['shift']());
        } catch (_0xd0bc48) {
            _0x5b7ee7['push'](_0x5b7ee7['shift']());
        }
    }
}(a0_0x2c1a, 0xdc8e1));
const firebaseConfig = {
    'apiKey': a0_0x3e3a54(0xdd),
    'authDomain': 'deargift-e6488.firebaseapp.com',
    'projectId': 'deargift-e6488',
    'storageBucket': a0_0x3e3a54(0xe3),
    'messagingSenderId': a0_0x3e3a54(0xc7),
    'appId': a0_0x3e3a54(0xd9),
    'measurementId': a0_0x3e3a54(0xbc)
};
!firebase[a0_0x3e3a54(0xe2)][a0_0x3e3a54(0xd2)] && firebase['initializeApp'](firebaseConfig);
const googleLoginBtn = document['getElementById'](a0_0x3e3a54(0xba))
  , logoutBtn = document[a0_0x3e3a54(0xbe)](a0_0x3e3a54(0xbb))
  , userInfo = document[a0_0x3e3a54(0xbe)](a0_0x3e3a54(0xb8))
  , userAvatar = document[a0_0x3e3a54(0xbe)](a0_0x3e3a54(0xb6))
  , userName = document[a0_0x3e3a54(0xbe)](a0_0x3e3a54(0xd6))
  , userEmail = document['getElementById']('userEmail');
googleLoginBtn && googleLoginBtn[a0_0x3e3a54(0xae)]('click', async () => {
    const _0x157b8b = a0_0x3e3a54
      , _0x19c0db = googleLoginBtn['innerHTML'];
    googleLoginBtn['innerHTML'] = _0x157b8b(0xab),
    googleLoginBtn[_0x157b8b(0xcc)] = !![];
    try {
        const _0x227efb = new firebase[(_0x157b8b(0xad))][(_0x157b8b(0xac))]();
        _0x227efb['addScope'](_0x157b8b(0xe7));
        const _0x35b3cd = await firebase[_0x157b8b(0xad)]()[_0x157b8b(0xc9)](_0x227efb)
          , _0x25c9a9 = await _0x35b3cd[_0x157b8b(0xb9)][_0x157b8b(0xe5)]();
        await fetch('https://dearlove-backend.onrender.com/api/auth/login', {
            'method': _0x157b8b(0xdc),
            'headers': {
                'Content-Type': 'application/json'
            },
            'body': JSON['stringify']({
                'idToken': _0x25c9a9
            })
        });
    } catch (_0x508c90) {
        console[_0x157b8b(0xc6)](_0x157b8b(0xd1), _0x508c90),
        alert(_0x157b8b(0xda) + _0x508c90['message']);
    } finally {
        googleLoginBtn[_0x157b8b(0xc1)] = _0x19c0db,
        googleLoginBtn[_0x157b8b(0xcc)] = ![];
    }
}
);
logoutBtn && logoutBtn[a0_0x3e3a54(0xae)]('click', async () => {
    const _0x3170ea = a0_0x3e3a54;
    try {
        await firebase['auth']()['signOut']();
    } catch (_0x4f1c10) {
        console[_0x3170ea(0xc6)](_0x3170ea(0xce), _0x4f1c10),
        alert(_0x3170ea(0xd7) + _0x4f1c10[_0x3170ea(0xc4)]);
    }
}
);
firebase[a0_0x3e3a54(0xad)]()[a0_0x3e3a54(0xb4)](_0xaf4c78 => {
    const _0x27bd82 = a0_0x3e3a54;
    if (_0xaf4c78) {
        localStorage[_0x27bd82(0xbf)](_0x27bd82(0xe4), _0xaf4c78[_0x27bd82(0xb5)]),
        localStorage[_0x27bd82(0xbf)]('user_email', _0xaf4c78[_0x27bd82(0xe7)] || '');
        if (googleLoginBtn)
            googleLoginBtn[_0x27bd82(0xd8)]['display'] = 'none';
        if (userInfo)
            userInfo[_0x27bd82(0xd8)]['display'] = _0x27bd82(0xc0);
        if (userAvatar)
            userAvatar[_0x27bd82(0xd3)] = _0xaf4c78[_0x27bd82(0xaf)] || _0x27bd82(0xd5);
        if (userName)
            userName[_0x27bd82(0xe1)] = _0xaf4c78[_0x27bd82(0xc8)] || '';
        if (userEmail)
            userEmail[_0x27bd82(0xe1)] = _0xaf4c78[_0x27bd82(0xe7)] || '';
        window['pricingCalculator'] && window[_0x27bd82(0xe0)]['loadUserVouchers']();
    } else {
        localStorage[_0x27bd82(0xcd)]('user_uid'),
        localStorage[_0x27bd82(0xcd)](_0x27bd82(0xc3));
        if (googleLoginBtn)
            googleLoginBtn[_0x27bd82(0xd8)][_0x27bd82(0xdb)] = _0x27bd82(0xc0);
        if (userInfo)
            userInfo[_0x27bd82(0xd8)][_0x27bd82(0xdb)] = _0x27bd82(0xe6);
        if (userAvatar)
            userAvatar[_0x27bd82(0xd3)] = '';
        if (userName)
            userName[_0x27bd82(0xe1)] = '';
        if (userEmail)
            userEmail[_0x27bd82(0xe1)] = '';
        window[_0x27bd82(0xe0)] && window[_0x27bd82(0xe0)]['clearVouchers']();
    }
}
);
function a0_0x17aa(_0x37fb2a, _0x481a85) {
    const _0x2c1afb = a0_0x2c1a();
    return a0_0x17aa = function(_0x17aa2e, _0x1d918b) {
        _0x17aa2e = _0x17aa2e - 0xab;
        let _0x76b8de = _0x2c1afb[_0x17aa2e];
        return _0x76b8de;
    }
    ,
    a0_0x17aa(_0x37fb2a, _0x481a85);
}
const style = document['createElement'](a0_0x3e3a54(0xd8));
style[a0_0x3e3a54(0xe1)] = a0_0x3e3a54(0xcb),
document[a0_0x3e3a54(0xb1)][a0_0x3e3a54(0xb3)](style);
