<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Website Cloner Bookmarklet</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .bookmarklet {
            background: #4CAF50;
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: bold;
            display: inline-block;
            margin: 20px 0;
            border: 2px solid #45a049;
        }
        .bookmarklet:hover {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .instructions {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .code {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
            word-break: break-all;
        }
        .step {
            margin: 15px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔓 Bypass Console Protection - Website Cloner</h1>
        
        <div class="instructions">
            <h2>📌 Method 1: Bookmarklet (Recommended)</h2>
            <div class="step">
                <strong>Step 1:</strong> Drag this button to your bookmarks bar:
                <br><br>
                <a href="javascript:(function(){var html=document.documentElement.outerHTML;var blob=new Blob([html],{type:'text/html'});var url=URL.createObjectURL(blob);var a=document.createElement('a');a.href=url;a.download='cloned-website.html';document.body.appendChild(a);a.click();document.body.removeChild(a);URL.revokeObjectURL(url);alert('Website cloned! Check Downloads folder.');})();" class="bookmarklet">
                    📥 Clone Website
                </a>
            </div>
            
            <div class="step">
                <strong>Step 2:</strong> Go to the birthday website:
                <br>https://happybirthdaydeargift.vercel.app/?websiteId=************************
            </div>
            
            <div class="step">
                <strong>Step 3:</strong> Click the "Clone Website" bookmark you just added
            </div>
            
            <div class="step">
                <strong>Step 4:</strong> The website will be downloaded to your Downloads folder!
            </div>
        </div>

        <div class="instructions">
            <h2>🛠️ Method 2: Manual Save</h2>
            <div class="step">
                <strong>Step 1:</strong> Go to the birthday website
            </div>
            <div class="step">
                <strong>Step 2:</strong> Press <code>Ctrl + S</code> (or Cmd + S on Mac)
            </div>
            <div class="step">
                <strong>Step 3:</strong> Choose "Webpage, Complete" in the save dialog
            </div>
            <div class="step">
                <strong>Step 4:</strong> Save it to your desired location
            </div>
        </div>

        <div class="instructions">
            <h2>💻 Method 3: PowerShell (Windows)</h2>
            <div class="step">
                <strong>Step 1:</strong> Press <code>Windows + R</code>, type <code>powershell</code>, press Enter
            </div>
            <div class="step">
                <strong>Step 2:</strong> Copy and paste this command:
                <div class="code">
Invoke-WebRequest -Uri "https://happybirthdaydeargift.vercel.app/?websiteId=************************" -OutFile "$env:USERPROFILE\Downloads\birthday-clone.html"
                </div>
            </div>
            <div class="step">
                <strong>Step 3:</strong> Press Enter and wait for download to complete
            </div>
        </div>

        <div class="instructions">
            <h2>🌐 Method 4: Online Tool</h2>
            <div class="step">
                <strong>Step 1:</strong> Go to: <a href="https://archive.today/" target="_blank" style="color: #4CAF50;">https://archive.today/</a>
            </div>
            <div class="step">
                <strong>Step 2:</strong> Paste the birthday website URL
            </div>
            <div class="step">
                <strong>Step 3:</strong> Click "Save" to create an archived copy
            </div>
            <div class="step">
                <strong>Step 4:</strong> Download the archived version
            </div>
        </div>

        <div class="instructions">
            <h2>🔧 Method 5: Browser Extension</h2>
            <div class="step">
                <strong>Step 1:</strong> Install "SingleFile" extension from Chrome Web Store
            </div>
            <div class="step">
                <strong>Step 2:</strong> Go to the birthday website
            </div>
            <div class="step">
                <strong>Step 3:</strong> Click the SingleFile extension icon
            </div>
            <div class="step">
                <strong>Step 4:</strong> It will automatically download the complete page
            </div>
        </div>
    </div>

    <script>
        // Show instructions for bookmarklet
        window.onload = function() {
            alert('📌 Drag the green "Clone Website" button to your bookmarks bar, then use it on any website to clone it!');
        };
    </script>
</body>
</html>
