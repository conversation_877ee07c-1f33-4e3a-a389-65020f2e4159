<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Chúc Mừng Sinh Nhật! 🎉</title>
    <meta name="language" content="zh-CN">
    <meta name="description" content="Một lời chúc sinh nhật đặc biệt dành cho bạn! Hãy khám phá món quà ý nghĩa này!">

    <!-- Favicon and Icons -->
    <link rel="icon" href="./image/logo.png" type="image/png">
    <link rel="apple-touch-icon" href="./image/logo.png">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Chúc Mừng Sinh Nhật! 🎂">
    <meta property="og:description"
        content="Một món quà sinh nhật đầy yêu thương đang chờ bạn khám phá! Nhấn để xem lời chúc đặc biệt!">
    <meta property="og:image" content="https://happybirthdaydeargift.vercel.app/image/logo.png">
    <!-- <meta property="og:url" content="https://happybirthdaydeargift.vercel.app/?websiteId=************************"> -->
    <meta property="og:type" content="website">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Chúc Mừng Sinh Nhật! 🎂">
    <meta name="twitter:description"
        content="Một món quà sinh nhật đầy yêu thương đang chờ bạn khám phá! Nhấn để xem lời chúc đặc biệt!">
    <meta name="twitter:image" content="https://happybirthdaydeargift.vercel.app/image/birthday-gift.jpg">

    <link rel="icon" type="image/x-icon" href="https://cdn.jsdelivr.net/gh/sun0225SUN/photos/img/20210715233345.png">
    <link rel="stylesheet" href="./css/index.css">
    <link rel="stylesheet" href="./css/protection.css">
    <link href="https://fonts.googleapis.com/css?family=Pacifico&display=swap" rel="stylesheet">
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
</head>

<body>
    <button id="langSwitchBtn"
        style="position:fixed;top:20px;right:150px;z-index:10000004;padding:8px 16px;border-radius:8px;background:#fff;color:#222;border:1px solid #dadce0;cursor:pointer;">
        EN/VI
    </button>
    <!-- Google Login Button -->
    <div id="googleAuthContainer" style="
        position: fixed;
        top: 20px;
        left: 20px;
        z-index: 10003;
        font-family: Arial, sans-serif;
    ">
        <button id="googleLoginBtn" style="
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: none;
            border: 1px solid #dadce0;
            border-radius: 24px;
            font-size: 14px;
            font-weight: 500;
            color: #3c4043;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        ">
            <svg width="18" height="18" viewBox="0 0 24 24">
                <path fill="#4285f4"
                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
                <path fill="#34a853"
                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />
                <path fill="#fbbc05"
                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" />
                <path fill="#ea4335"
                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
            </svg>
        </button>

        <div id="userInfo" style="
            display: none;
            align-items: center;
            padding: 8px 12px;
            background: none;
            border: 1px solid #dadce0;
            border-radius: 24px;
            font-size: 14px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        ">
            <img id="userAvatar" style="
                width: 24px;
                height: 24px;
                border-radius: 50%;
                margin-right: 8px;
            ">
            <div style="margin-right: 12px;">
                <div id="userName" style="font-weight: 500; color: #ffffff;"></div>
                <div id="userEmail" style="font-size: 12px; color: #ffffff;"></div>
            </div>
            <button id="logoutBtn"
                style="padding: 4px 8px; background: none; border: 1px solid #dadce0; border-radius: 4px; font-size: 12px; cursor: pointer; color: #fbfbfc;"
                data-i18n="logout">Đăng xuất</button>
        </div>
    </div>


    </div>
    <canvas id="matrix-rain" style="display: none;"></canvas>
    <canvas style="position:absolute;width:100%;height:100%;z-index:9999;display:none;" class="canvas"></canvas>
    <div class="overlay">
        <div class="stars-container" id="starsContainer"></div>
        <img id="gift-image" src="happy.gif"
            style="display: none; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 10000; max-width: 80%; max-height: 80%;" />
        <div class="content-display" id="contentDisplay">
            <p id="contentText"></p>
        </div>
        <div class="firework-container" id="fireworkContainer"></div>
        <div class="book-container">
            <div class="book" id="book"></div>
        </div>

        <button class="music-control" id="musicControl" title="Play/Pause Music">
            ▶
        </button>

        <button class="settings-button" id="settingsButton" title="Settings">
            ⚙
        </button>

        <div id="settingsModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 data-i18n="settings">⚙️ Cài Đặt Trang Web</h2>
                    <span class="close">×</span>
                </div>
                <div class="modal-body">
                    <div class="settings-section">
                        <h3 data-i18n="music">🎵 Cài Đặt Âm Thanh</h3>
                        <div class="form-group">
                            <label for="backgroundMusic" data-i18n="backgroundMusic">Nhạc Nền:</label>
                            <select id="backgroundMusic">
                                <option value="./music/happybirtday_uia.mp3">Happy Birthday</option>
                                <option value="./music/dunglamtraitimanhdau.mp3">Đừng làm trái tim anh đau</option>
                            </select>
                        </div>
                    </div>

                    <div class="settings-section">
                        <h3 data-i18n="countdown">⏰ Cài Đặt Thời Gian</h3>
                        <div class="form-group">
                            <label for="countdownTime" data-i18n="countdownTime">Thời Gian Đếm Ngược:</label>
                            <select id="countdownTime">
                                <option value="3" data-i18n="sec3">3 giây</option>
                                <option value="5" data-i18n="sec5">5 giây</option>
                                <option value="10" data-i18n="sec10">10 giây</option>
                            </select>
                        </div>
                    </div>

                    <div class="settings-section">
                        <h3 data-i18n="matrix">🌧️ Cài Đặt Hiệu Ứng Mưa Chữ</h3>
                        <div class="form-group">
                            <label for="matrixText" data-i18n="matrixText">chữ chính mưa chữ:</label>
                            <input type="text" id="matrixText" value="HAPPYBIRTHDAY"
                                placeholder="Nhập chữ cho hiệu ứng mưa chữ" data-i18n-placeholder="matrixText">
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="matrixColor1" data-i18n="matrixColor1">Màu mưa chữ 1:</label>
                                <input type="color" id="matrixColor1" value="#ffb6c1">

                            </div>
                            <div class="form-group">
                                <label for="matrixColor2" data-i18n="matrixColor2">Màu mưa chữ 2:</label>
                                <input type="color" id="matrixColor2" value="#ffc0cb">

                            </div>
                        </div>
                    </div>

                    <div class="settings-section">
                        <h3 data-i18n="sequence">✨ Cài Đặt chữ chính</h3>
                        <div class="form-group">
                            <label for="sequenceText" data-i18n="sequenceText">Nội Dung chữ chính:</label>
                            <span data-i18n="noteSequence"
                                style="color: rgb(132, 132, 132); font-weight: normal; font-size: 12px; display: block; margin-bottom: 5px;">
                                lưu ý: hãy ngăn cách bằng dấu | để tách từ, không nên để một dòng quá dài
                            </span>
                            <input type="text" id="sequenceText" value="HAPPY|BIRTHDAY|MY|CUTEE|LITTLE|SWARALI|❤"
                                placeholder="Ví dụ: CHÚC|MỪNG|SINH|NHẬT|..." data-i18n-placeholder="sequenceText">
                        </div>
                        <div class="form-group">
                            <label for="sequenceColor" data-i18n="sequenceColor">Màu chữ chính:</label>
                            <input type="color" id="sequenceColor" value="#d39b9b">
                        </div>
                    </div>

                    <div class="settings-section">
                        <h3 data-i18n="gift">🎁 Cài Đặt Hình Động</h3>
                        <div class="form-group">
                            <label for="giftImage" data-i18n="giftImage">Hình Động (tùy chọn):</label>
                            <select id="giftImage">
                                <option value="" data-i18n="noGif">Không có</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="enableBook" data-i18n="enableBook">Hiển thị sách:</label>
                        <select id="enableBook">
                            <option value="true" data-i18n="on">Bật</option>
                            <option value="false" data-i18n="off">Tắt</option>
                        </select>
                    </div>
                    <div class="settings-section" id="bookSettingsSection">
                        <h3 data-i18n="book">📖 Cài Đặt Trang Sách</h3>
                        <div id="pageConfigs">
                        </div>
                        <div class="form-group">
                            <label for="enableHeart" data-i18n="enableHeart">Hiển thị hiệu ứng trái tim:</label>
                            <select id="enableHeart">
                                <option value="true" data-i18n="on">Bật</option>
                                <option value="false" data-i18n="off">Tắt</option>
                            </select>
                        </div>
                    </div>
                    <div
                        style="margin-bottom: 12px; background: #fff8e1; color: #b26a00; border-radius: 7px; padding: 10px 14px; font-size: 15px; box-shadow: 0 1px 6px rgba(0,0,0,0.04);">
                        <div style="margin-bottom: 4px;" data-i18n="noteExpire">
                            ⏳ <b>Lưu ý:</b> Hạn sử dụng chỉ có <b>30 ngày</b>, vậy nên đừng tạo trước quá sớm nhé!
                        </div>
                        <div data-i18n="followNote">
                            🥰 Hãy <a href="https://www.tiktok.com/@iamtritoan?is_from_webapp=1&sender_device=pc"
                                target="_blank" style="color:#ff0050;font-weight:bold;text-decoration:underline;">follow
                                mình</a> để cập nhật những website mới nhất, nhanh nhất nhé!
                        </div>
                        <div data-i18n="notVietnamWarning">
                            ⚠️ Nếu bạn không phải người Việt Nam, bạn sẽ không thanh toán được qua website này, hãy nhắn
                            tin qua TikTok
                            <a href="https://www.tiktok.com/@iamtritoan?is_from_webapp=1&sender_device=pc"
                                target="_blank" style="color:#ff0050;font-weight:bold;text-decoration:underline;">
                                @iamtritoan</a> để được hỗ trợ thanh toán sản phẩm này.
                        </div>
                    </div>
                    <button id="applySettings" class="apply-settings-btn" data-i18n="apply">🎉 Áp Dụng Cài Đặt</button>
                </div>
            </div>
        </div>

        <audio id="birthdayAudio" loop>
            <source src="./music/happybirtday_uia.mp3" type="audio/mpeg">
            Your browser does not support the audio element.
        </audio>

        <div class="copyright" data-i18n="copyright">
            Website được tạo bởi tiktok: <a
                href="https://www.tiktok.com/@iamtritoan?is_from_webapp=1&sender_device=pc">iamtritoan</a>
        </div>

        <div id="orientation-lock">
            <div class="phone-icon">
                <img src="./image/screen.png" alt="">
            </div>
        </div>
        <!-- Modal Payment-->
        <div id="paymentModal"
            style="display:none; position:fixed; top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,0.5);z-index:99999;">
            <div
                style="position:relative; margin: 5% auto; width: 90%; max-width: 800px; background:white; padding:20px; border-radius:10px;">
                <button onclick="document.getElementById('paymentModal').style.display='none'"
                    style="position:absolute;top:10px;right:10px;">✖</button>
                <iframe id="paymentIframe" style="width:100%; height:600px; border:none;"></iframe>
            </div>
        </div>
        <!-- Nút Full màn hình, chỉ hiện trên Android -->
        <button id="fullscreenBtn" style="
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 990010;
    padding: 10px 18px;
    border-radius: 8px;
    border: none;
    background: #222;
    color: #fff;
    font-size: 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    cursor: pointer;
    display: none;
">
            <img style="
    width: 20px;
    height: 20px;
    vertical-align: middle;
" src="./image/full.png" alt="">
        </button>

        <script src="./lang.js"></script>

        <script src="jscp/api.js"></script>
        <script src="./jscp/auth.js"></script>
        <script src="./jscp/settings.js"></script>
        <script src="./jscp/ui.js"></script>
        <script src="./jscp/main.js"></script>
        <script src="./jscp/protection.js"></script>

        <!-- Prevent payment modal from showing -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Remove any existing payment modal triggers
                document.querySelectorAll('[onclick*="payment"]').forEach(el => {
                    el.onclick = null;
                });
                
                // Override any payment functions
                window.showPaymentModal = function() {};
                window.openPayment = function() {};
            });
        </script>

</body>

</html>