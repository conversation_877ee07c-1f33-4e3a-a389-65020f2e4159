// Placeholder for lang.js. Add your language logic here.
const translations = {
    vi: {
        title: "<PERSON>úc Mừng Sinh Nhật! 🎉",
        description: "Một lời chúc sinh nhật đặc biệt dành cho bạn! Hãy khám phá món quà ý nghĩa này!",
        login: "Đăng nhập bằng Google",
        logout: "Đăng xuất",
        settings: "Cài Đặt Trang Web",
        music: "Cài Đặt Âm Thanh",
        backgroundMusic: "Nhạc Nền:",
        countdown: "Cài Đặt Thời Gian",
        countdownTime: "Thời Gian Đếm Ngược:",
        matrix: "Cài Đặt Hiệu Ứng Mưa Chữ",
        matrixText: "chữ chính mưa chữ:",
        matrixColor1: "Màu mưa chữ 1:",
        matrixColor2: "Màu mưa chữ 2:",
        sequence: "Cài Đặt chữ chính",
        sequenceText: "<PERSON>ội Dung chữ chính:",
        sequenceColor: "<PERSON><PERSON><PERSON> chữ chính:",
        gift: "Cài Đặt Hình Động",
        giftImage: "<PERSON><PERSON><PERSON>ộng (tùy chọn):",
        enableBook: "Hiển thị sách:",
        book: "Cài Đặt Trang Sách",
        enableHeart: "Hiển thị hiệu ứng trái tim:",
        note: "⏳ Lưu ý: Hạn sử dụng chỉ có 7 ngày, vậy nên đừng tạo trước quá sớm nhé!",
        follow: "🥰 Hãy follow mình để cập nhật những website mới nhất, nhanh nhất nhé!",
        apply: "Áp Dụng Cài Đặt",
        copyright: 'Website được tạo bởi tiktoker: <a href="https://www.tiktok.com/@iamtritoan?is_from_webapp=1&sender_device=pc" target="_blank"> @iamtritoan</a>',
        fullscreen: "Toàn màn hình",
        // ...thêm các key khác nếu cần...
        pricingTitle: "Bảng Giá Dịch Vụ",
        voucher: "Voucher giảm giá:",
        tip: "💸 Tip cho tác giả:",
        total: "Tổng cộng:",
        createWebsite: "🎉 Tạo Website",
        payAndCreate: "💳 Thanh toán & Tạo Website",
        defaultConfig: "🎉 Sử dụng cấu hình mặc định - MIỄN PHÍ!",
        customMusic: "Nhạc nền tùy chỉnh",
        book: "Sách kỷ niệm",
        extraPage: "Trang thêm",
        heartEffect: "Hiệu ứng trái tim (trong sách)",
        tipAuthor: "Tip cho tác giả",
        voucherApplied: "✅ Đã áp dụng voucher: {code} (-{discount}%)",
        loadingVoucher: "Đang tải voucher...",
        noVoucher: "Bạn không có voucher nào cả!",
        needLoginVoucher: "Bạn cần đăng nhập để xem voucher!",
        cannotLoadVoucher: "Không thể tải voucher!",
        copyLink: "📋 Copy Link",
        viewWebsite: "🔗 Xem Website",
        close: "✖ Đóng",
        createSuccess: "🎉 Tạo website thành công!",
        price: "Giá:",
        shareLink: "Link chia sẻ:",
        copySuccess: "Đã copy link sản phẩm!",
        needLogin: "❌ Bạn cần đăng nhập để tạo website!",
        minPay: "❌ Số tiền thanh toán tối thiểu là 10.000 VNĐ!",
        error: "❌ Có lỗi xảy ra!",
        invalidPage: "❌ Cấu trúc trang không hợp lệ! Hiện tại có {pages} trang. Vui lòng thêm hoặc xóa 1 trang để tạo cấu trúc hợp lệ.",
        uploading: "📤 Đang upload ảnh trang sách...",
        creating: "🌐 Đang tạo website...",
        creatingProduct: "📦 Đang tạo sản phẩm...",
        applyingVoucher: "🎫 Đang áp dụng voucher...",
        applyVoucherSuccess: "✅ Áp dụng voucher thành công!",
        applyVoucherFail: "⚠️ Lỗi áp dụng voucher, tiếp tục thanh toán với giá gốc",
        cannotCreate: "Không thể tạo website",
        cannotCreateProduct: "Lỗi khi tạo sản phẩm",
        cannotApplyVoucher: "Áp dụng voucher thất bại!",
        cannotPay: "Lỗi xử lý thanh toán: ",
        paymentRedirect: "🔄 Đang chuyển đến trang thanh toán...",
        free: "MIỄN PHÍ",
        thanks: "Cảm ơn bạn rất nhiều! Nếu thích sản phẩm này, hãy follow kênh TikTok <b>tớ là Toán</b> để mình có động lực ra nhiều sản phẩm hơn nhé!",
        tiktokBtn: "🥰 xem kênh TikTok",
        heartQr: "❤️ Tạo QR trái tim",
        heartQrCopy: "✅ Đã copy link QR trái tim!",
        copyShare: "📋 Sao chép link",
        copied: "✅ Đã sao chép!",
        copyToClipboard: "📋 Đã sao chép link vào clipboard!",
        copyFail: "❌ Không thể sao chép link!",

        //
        validCoverOnly: "✅ Hợp lệ (chỉ có bìa)",
        validCoverAndPairs: "✅ Hợp lệ (bìa + các cặp trang)",
        invalidMissingPage: "❌ Không hợp lệ (thiếu 1 trang để tạo cặp)",
        bookPageInfo: "Thông tin trang sách:",
        totalPages: "Tổng số trang",
        status: "Trạng thái",
        structure: "Cấu trúc",
        coverOnly: "Chỉ có bìa",
        coverAndPairs: "Bìa (1) + {pairs} cặp trang{extra}",
        plusOneExtra: " + 1 trang lẻ",
        pageTitleCover: "Trang {num} (Bìa)",
        pageTitle: "Trang {num}",
        imageLabel: "Hình ảnh:",
        coverPlaceholder: "Bìa Sách",
        pagePlaceholder: "Trang {num}",
        noImageAlt: "Chưa có ảnh - {placeholder}",
        contentLabel: "Nội dung:",
        contentPlaceholder: "Nhập nội dung cho trang {num}",
        addNewPage: "➕ Thêm Trang Mới",
        emptyPage: "Trang trống",
        endOfBook: "Hết sách",
        loading: "Đang tải...",
        waitingIsHappiness: "Chờ đợi là hạnh phúc!",
        invalidPageStructure: "Cấu trúc trang không hợp lệ!",
        currentPages: "Hiện tại có {total} trang.",
        bookStructureGuide: "Cấu trúc sách cần:\n- Trang 1: Bìa\n- Từ trang 2 trở đi: Các cặp trang (2-3, 4-5, 6-7...)",
        pleaseAddOrRemovePage: "Vui lòng thêm thêm 1 trang hoặc xóa bớt 1 trang để tạo cấu trúc hợp lệ.",
        fullscreenNotSupported: "Trình duyệt của bạn không hỗ trợ chế độ toàn màn hình!",
        noteSequence: "lưu ý: hãy ngăn cách bằng dấu | để tách từ, không nên để một dòng quá dài",
        noteExpire: "⏳ <b>Lưu ý:</b> Hạn sử dụng chỉ có <b>30 ngày</b>, vậy nên đừng tạo trước quá sớm nhé!",
        followNote: "🥰 Hãy <a href=\"https://www.tiktok.com/@iamtritoan?is_from_webapp=1&sender_device=pc\" target=\"_blank\" style=\"color:#ff0050;font-weight:bold;text-decoration:underline;\">follow mình</a> để cập nhật những website mới nhất, nhanh nhất nhé!",
        on: "Bật",
        off: "Tắt",
        sec3: "3 giây",
        sec5: "5 giây",
        sec10: "10 giây",
        noGif: "Không có",
        notVietnamWarning: '⚠️ Nếu bạn không phải người Việt Nam, bạn sẽ không thanh toán được qua website này. Hãy thiết kế sản phẩm của bạn xong, sau đó ấn nút "💳 Thanh toán & Tạo Website" và đợi đến khi hiển thị mã QR thanh toán. Lúc này, hãy nhắn tin qua TikTok<a href="https://www.tiktok.com/@iamtritoan?is_from_webapp=1&sender_device=pc" target="_blank" style="color:#ff0050;font-weight:bold;text-decoration:underline;"> @iamtritoan</a> để được hỗ trợ thanh toán sản phẩm này.',
    },
    en: {
        title: "Happy Birthday! 🎉",
        description: "A special birthday wish just for you! Discover this meaningful gift!",
        login: "Sign in with Google",
        logout: "Logout",
        settings: "Website Settings",
        music: "Music Settings",
        backgroundMusic: "Background Music:",
        countdown: "Countdown Settings",
        countdownTime: "Countdown Time:",
        matrix: "Matrix Rain Effect Settings",
        matrixText: "Matrix main text:",
        matrixColor1: "Matrix color 1:",
        matrixColor2: "Matrix color 2:",
        sequence: "Main Text Settings",
        sequenceText: "Main text content:",
        sequenceColor: "Main text color:",
        gift: "Animated Image Settings",
        giftImage: "Animated Image (optional):",
        enableBook: "Show book:",
        book: "Book Page Settings",
        enableHeart: "Show heart effect:",
        note: "⏳ Note: The website is valid for only 7 days, so don't create it too early!",
        follow: "🥰 Please follow me to get the latest websites as soon as possible!",
        apply: "Apply Settings",
        copyright: 'Website created by tiktoker:<a href="https://www.tiktok.com/@iamtritoan?is_from_webapp=1&sender_device=pc" target="_blank"> @iamtritoan</a>',
        fullscreen: "Fullscreen",
        // ...add more keys as needed...

        pricingTitle: "Service Pricing",
        voucher: "Discount voucher:",
        tip: "💸 Tip for author:",
        total: "Total:",
        createWebsite: "🎉 Create Website",
        payAndCreate: "💳 Pay & Create Website",
        defaultConfig: "🎉 Using default configuration - FREE!",
        customMusic: "Custom background music",
        book: "Memory book",
        extraPage: "Extra page",
        heartEffect: "Heart effect (in book)",
        tipAuthor: "Tip for author",
        voucherApplied: "✅ Voucher applied: {code} (-{discount}%)",
        loadingVoucher: "Loading vouchers...",
        noVoucher: "You have no vouchers!",
        needLoginVoucher: "You need to login to see vouchers!",
        cannotLoadVoucher: "Cannot load vouchers!",
        copyLink: "📋 Copy Link",
        viewWebsite: "🔗 View Website",
        close: "✖ Close",
        createSuccess: "🎉 Website created successfully!",
        price: "Price:",
        shareLink: "Share link:",
        copySuccess: "Link copied!",
        needLogin: "❌ Please login to create website!",
        minPay: "❌ Minimum payment is 10,000 VND!",
        error: "❌ An error occurred!",
        invalidPage: "❌ Invalid page structure! Currently {pages} pages. Please add or remove 1 page for valid structure.",
        uploading: "📤 Uploading book images...",
        creating: "🌐 Creating website...",
        creatingProduct: "📦 Creating product...",
        applyingVoucher: "🎫 Applying voucher...",
        applyVoucherSuccess: "✅ Voucher applied successfully!",
        applyVoucherFail: "⚠️ Voucher error, continue with original price",
        cannotCreate: "Cannot create website",
        cannotCreateProduct: "Error creating product",
        cannotApplyVoucher: "Voucher apply failed!",
        cannotPay: "Payment error: ",
        paymentRedirect: "🔄 Redirecting to payment page...",
        free: "FREE",
        thanks: "Thank you so much! If you like this product, please follow my TikTok channel <b>tớ là Toán</b> to motivate me to make more!",
        tiktokBtn: "🥰 View TikTok channel",
        heartQr: "❤️ Create heart QR",
        heartQrCopy: "✅ Heart QR link copied!",
        copyShare: "📋 Copy link",
        copied: "✅ Copied!",
        copyToClipboard: "📋 Link copied to clipboard!",
        copyFail: "❌ Cannot copy link!",

        //
        validCoverOnly: "✅ Valid (cover only)",
        validCoverAndPairs: "✅ Valid (cover + page pairs)",
        invalidMissingPage: "❌ Invalid (missing 1 page to form a pair)",
        bookPageInfo: "Book page info:",
        totalPages: "Total pages",
        status: "Status",
        structure: "Structure",
        coverOnly: "Cover only",
        coverAndPairs: "Cover (1) + {pairs} page pairs{extra}",
        plusOneExtra: " + 1 extra page",
        pageTitleCover: "Page {num} (Cover)",
        pageTitle: "Page {num}",
        imageLabel: "Image:",
        coverPlaceholder: "Book Cover",
        pagePlaceholder: "Page {num}",
        noImageAlt: "No image yet - {placeholder}",
        contentLabel: "Content:",
        contentPlaceholder: "Enter content for page {num}",
        addNewPage: "➕ Add New Page",
        emptyPage: "Empty page",
        endOfBook: "End of book",
        loading: "Loading...",
        waitingIsHappiness: "Waiting is happiness!",
        invalidPageStructure: "Invalid page structure!",
        currentPages: "Currently {total} pages.",
        bookStructureGuide: "Book structure required:\n- Page 1: Cover\n- From page 2: Page pairs (2-3, 4-5, 6-7...)",
        pleaseAddOrRemovePage: "Please add or remove 1 page to create a valid structure.",
        fullscreenNotSupported: "Your browser does not support fullscreen mode!",
        // Thêm vào cả vi và en:
noteSequence: "lưu ý: hãy ngăn cách bằng dấu | để tách từ, không nên để một dòng quá dài",
noteExpire: "⏳ <b>Note:</b> The website is only valid for <b>30 days</b>, so don't create it too early!",
followNote: "🥰 Please <a href=\"https://www.tiktok.com/@iamtritoan?is_from_webapp=1&sender_device=pc\" target=\"_blank\" style=\"color:#ff0050;font-weight:bold;text-decoration:underline;\">follow me</a> to get the latest websites as soon as possible!",
on: "On",
off: "Off",
sec3: "3 seconds",
sec5: "5 seconds",
sec10: "10 seconds",
noGif: "None",
notVietnamWarning: '⚠️ If you are not Vietnamese, you cannot pay through this website. Please finish designing your product, then click the "💳 Pay & Create Website" button and wait until the payment QR code is displayed. At that time, please message TikTok<a href="https://www.tiktok.com/@iamtritoan?is_from_webapp=1&sender_device=pc" target="_blank" style="color:#ff0050;font-weight:bold;text-decoration:underline;"> @iamtritoan</a> for payment support.',
    }
};

function setLanguage(lang) {
    document.documentElement.lang = lang;
    // Đổi title và meta
    document.title = translations[lang].title;
    document.querySelector('meta[name="description"]').setAttribute('content', translations[lang].description);

    // Đổi các phần tử có data-i18n
    document.querySelectorAll('[data-i18n]').forEach(el => {
        const key = el.getAttribute('data-i18n');
        if (translations[lang][key]) {
            if (
                translations[lang][key].includes('<b>') ||
                translations[lang][key].includes('<a')
            ) {
                el.innerHTML = translations[lang][key];
            } else {
                el.innerText = translations[lang][key];
            }
        }
    });

    // Đổi các placeholder
    document.querySelectorAll('[data-i18n-placeholder]').forEach(el => {
        const key = el.getAttribute('data-i18n-placeholder');
        if (translations[lang][key]) {
            el.setAttribute('placeholder', translations[lang][key]);
        }
    });

    // Đổi các label, option, v.v. nếu cần
    // ...bổ sung nếu cần...
}

// Lưu ngôn ngữ vào localStorage
function switchLanguage() {
    const current = localStorage.getItem('lang') || 'vi';
    const next = current === 'vi' ? 'en' : 'vi';
    localStorage.setItem('lang', next);
    setLanguage(next);
       // Cập nhật lại UI động (nếu có)
    if (window.pricingCalculator) {
        window.pricingCalculator.updatePricing();
    }
}

// Khởi tạo ngôn ngữ khi load trang
document.addEventListener('DOMContentLoaded', () => {
    const lang = localStorage.getItem('lang') || 'vi';
    setLanguage(lang);
    // Gắn sự kiện cho nút chuyển ngôn ngữ
    document.getElementById('langSwitchBtn').onclick = switchLanguage;
});

function t(key, vars = {}) {
    const lang = localStorage.getItem('lang') || 'vi';
    let str = (translations[lang] && translations[lang][key]) || key;
    Object.keys(vars).forEach(k => {
        str = str.replace(`{${k}}`, vars[k]);
    });
    return str;
}

