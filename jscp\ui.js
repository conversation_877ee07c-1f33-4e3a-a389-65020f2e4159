// UI Management for Birthday Website
class UIManager {
    constructor() {
        this.isFullscreen = false;
        this.isMobile = this.detectMobile();
        this.init();
    }

    init() {
        this.setupResponsiveDesign();
        this.setupFullscreenButton();
        this.setupModalHandlers();
        this.setupLanguageSwitch();
        this.setupOrientationHandler();
        this.setupAccessibility();
    }

    detectMobile() {
        return window.innerWidth <= 768 || 
               /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               ('ontouchstart' in window) ||
               (navigator.maxTouchPoints > 0);
    }

    setupResponsiveDesign() {
        // Adjust UI elements based on screen size
        const adjustForScreenSize = () => {
            const isMobile = this.detectMobile();
            const isLandscape = window.innerWidth > window.innerHeight;
            
            // Adjust modal size for mobile
            const modal = document.querySelector('.modal-content');
            if (modal) {
                if (isMobile) {
                    modal.style.width = '95%';
                    modal.style.height = isLandscape ? '90vh' : '85vh';
                    modal.style.margin = '2.5vh auto';
                } else {
                    modal.style.width = '90%';
                    modal.style.height = '80vh';
                    modal.style.margin = '5vh auto';
                }
            }

            // Adjust book size for mobile landscape
            const book = document.querySelector('.book');
            if (book && isMobile && isLandscape) {
                book.style.transform = 'scale(0.7)';
            } else if (book) {
                book.style.transform = 'scale(1)';
            }

            // Adjust content display for mobile
            const contentDisplay = document.querySelector('.content-display');
            if (contentDisplay && isMobile) {
                if (isLandscape) {
                    contentDisplay.style.width = '45vw';
                    contentDisplay.style.padding = '15px 20px';
                } else {
                    contentDisplay.style.width = '85vw';
                    contentDisplay.style.padding = '25px 30px';
                }
            }
        };

        // Initial adjustment
        adjustForScreenSize();

        // Adjust on resize
        window.addEventListener('resize', adjustForScreenSize);
    }

    setupFullscreenButton() {
        const fullscreenBtn = document.getElementById('fullscreenBtn');
        if (!fullscreenBtn) return;

        // Show fullscreen button only on Android
        const isAndroid = /Android/i.test(navigator.userAgent);
        if (isAndroid) {
            fullscreenBtn.style.display = 'block';
        }

        fullscreenBtn.addEventListener('click', () => {
            this.toggleFullscreen();
        });

        // Listen for fullscreen changes
        document.addEventListener('fullscreenchange', () => {
            this.isFullscreen = !!document.fullscreenElement;
            this.updateFullscreenButton();
        });
    }

    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.log('Fullscreen request failed:', err);
                this.showNotification(t('fullscreenNotSupported'), 'error');
            });
        } else {
            document.exitFullscreen();
        }
    }

    updateFullscreenButton() {
        const fullscreenBtn = document.getElementById('fullscreenBtn');
        if (fullscreenBtn) {
            const img = fullscreenBtn.querySelector('img');
            if (img) {
                img.style.transform = this.isFullscreen ? 'rotate(180deg)' : 'rotate(0deg)';
            }
        }
    }

    setupModalHandlers() {
        // Settings modal
        const settingsModal = document.getElementById('settingsModal');
        const settingsBtn = document.getElementById('settingsButton');
        const closeBtn = settingsModal?.querySelector('.close');

        if (settingsBtn && settingsModal) {
            settingsBtn.addEventListener('click', () => {
                this.openModal('settingsModal');
            });
        }

        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.closeModal('settingsModal');
            });
        }

        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal(e.target.id);
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal[style*="block"]');
                if (openModal) {
                    this.closeModal(openModal.id);
                }
            }
        });
    }

    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden'; // Prevent background scrolling
            
            // Focus management for accessibility
            const firstFocusable = modal.querySelector('input, button, select, textarea');
            if (firstFocusable) {
                firstFocusable.focus();
            }
        }
    }

    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = ''; // Restore scrolling
        }
    }

    setupLanguageSwitch() {
        // Language switching disabled - English only
        console.log('Language set to English only');
    }

    setupOrientationHandler() {
        const checkOrientation = () => {
            const orientationLock = document.getElementById('orientation-lock');
            if (!orientationLock) return;

            const isMobile = this.detectMobile();
            const isLandscape = window.innerWidth > window.innerHeight;

            if (isMobile && isLandscape) {
                orientationLock.style.display = 'flex';
            } else {
                orientationLock.style.display = 'none';
            }
        };

        // Check on load and resize
        checkOrientation();
        window.addEventListener('resize', checkOrientation);
        window.addEventListener('orientationchange', () => {
            setTimeout(checkOrientation, 100); // Delay to ensure dimensions are updated
        });
    }

    setupAccessibility() {
        // Add keyboard navigation for interactive elements
        const interactiveElements = document.querySelectorAll('button, .page, .music-control, .settings-button');
        
        interactiveElements.forEach(element => {
            // Make elements focusable
            if (!element.hasAttribute('tabindex')) {
                element.setAttribute('tabindex', '0');
            }

            // Add keyboard event listeners
            element.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    element.click();
                }
            });
        });

        // Add ARIA labels where missing
        const musicControl = document.getElementById('musicControl');
        if (musicControl && !musicControl.hasAttribute('aria-label')) {
            musicControl.setAttribute('aria-label', 'Play/Pause music');
        }

        const settingsButton = document.getElementById('settingsButton');
        if (settingsButton && !settingsButton.hasAttribute('aria-label')) {
            settingsButton.setAttribute('aria-label', 'Open settings');
        }
    }

    showNotification(message, type = 'info', duration = 3000) {
        // Remove existing notifications
        const existingNotifications = document.querySelectorAll('.ui-notification');
        existingNotifications.forEach(notification => notification.remove());

        // Create notification element
        const notification = document.createElement('div');
        notification.className = `ui-notification ui-notification-${type}`;
        notification.textContent = message;
        
        // Style the notification
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            max-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            animation: slideInRight 0.3s ease-out;
            font-family: Arial, sans-serif;
            font-size: 14px;
        `;

        // Set background color based on type
        const colors = {
            success: '#4CAF50',
            error: '#f44336',
            warning: '#ff9800',
            info: '#2196F3'
        };
        notification.style.backgroundColor = colors[type] || colors.info;

        // Add animation styles if not already present
        if (!document.getElementById('notification-styles')) {
            const style = document.createElement('style');
            style.id = 'notification-styles';
            style.textContent = `
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes slideOutRight {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }

        // Add to DOM
        document.body.appendChild(notification);

        // Auto remove after duration
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }, duration);

        return notification;
    }

    showLoading(message = 'Loading...') {
        // Remove existing loading
        this.hideLoading();

        const loading = document.createElement('div');
        loading.id = 'ui-loading';
        loading.innerHTML = `
            <div style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: rgba(0,0,0,0.7);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 99999;
                color: white;
                font-family: Arial, sans-serif;
            ">
                <div style="text-align: center;">
                    <div style="
                        width: 40px;
                        height: 40px;
                        border: 4px solid #f3f3f3;
                        border-top: 4px solid #ff6b9d;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                        margin: 0 auto 20px;
                    "></div>
                    <div style="font-size: 18px;">${message}</div>
                </div>
            </div>
        `;

        // Add spinner animation
        if (!document.getElementById('spinner-styles')) {
            const style = document.createElement('style');
            style.id = 'spinner-styles';
            style.textContent = `
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(loading);
        return loading;
    }

    hideLoading() {
        const loading = document.getElementById('ui-loading');
        if (loading) {
            loading.remove();
        }
    }

    updateProgress(percentage, message = '') {
        let progressBar = document.getElementById('ui-progress');
        
        if (!progressBar) {
            progressBar = document.createElement('div');
            progressBar.id = 'ui-progress';
            progressBar.innerHTML = `
                <div style="
                    position: fixed;
                    bottom: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 300px;
                    background: rgba(0,0,0,0.8);
                    color: white;
                    padding: 15px;
                    border-radius: 8px;
                    z-index: 10000;
                    font-family: Arial, sans-serif;
                ">
                    <div id="progress-message" style="margin-bottom: 10px; font-size: 14px;"></div>
                    <div style="
                        width: 100%;
                        height: 6px;
                        background: #333;
                        border-radius: 3px;
                        overflow: hidden;
                    ">
                        <div id="progress-fill" style="
                            height: 100%;
                            background: linear-gradient(90deg, #ff6b9d, #4ecdc4);
                            width: 0%;
                            transition: width 0.3s ease;
                        "></div>
                    </div>
                    <div id="progress-percent" style="
                        text-align: center;
                        margin-top: 5px;
                        font-size: 12px;
                        opacity: 0.8;
                    "></div>
                </div>
            `;
            document.body.appendChild(progressBar);
        }

        const fill = document.getElementById('progress-fill');
        const messageEl = document.getElementById('progress-message');
        const percentEl = document.getElementById('progress-percent');

        if (fill) fill.style.width = percentage + '%';
        if (messageEl) messageEl.textContent = message;
        if (percentEl) percentEl.textContent = Math.round(percentage) + '%';

        if (percentage >= 100) {
            setTimeout(() => {
                if (progressBar.parentNode) {
                    progressBar.remove();
                }
            }, 1000);
        }
    }
}

// Initialize UI manager
document.addEventListener('DOMContentLoaded', () => {
    window.uiManager = new UIManager();
});
