// Settings Management for Birthday Website
class SettingsManager {
    constructor() {
        this.defaultSettings = {
            backgroundMusic: './music/happybirtday_uia.mp3',
            countdownTime: 5,
            matrixText: 'HAPPYBIRTHDAY',
            matrixColor1: '#ffb6c1',
            matrixColor2: '#ffc0cb',
            sequenceText: 'HAPPY|BIRTHDAY|MY|CUTEE|LITTLE|SWARALI|❤',
            sequenceColor: '#d39b9b',
            giftImage: '',
            enableBook: true,
            enableHeart: true,
            pages: [
                {
                    content: 'Happy Birthday!',
                    image: './image/happy.gif'
                }
            ]
        };
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadAvailableGifs();
    }

    setupEventListeners() {
        // Enable/disable book settings based on enableBook
        const enableBookSelect = document.getElementById('enableBook');
        const bookSettingsSection = document.getElementById('bookSettingsSection');
        
        if (enableBookSelect && bookSettingsSection) {
            enableBookSelect.addEventListener('change', (e) => {
                if (e.target.value === 'true') {
                    bookSettingsSection.style.display = 'block';
                    this.initializeBookPages();
                } else {
                    bookSettingsSection.style.display = 'none';
                }
            });
        }

        // Add page button
        this.setupAddPageButton();
    }

    loadAvailableGifs() {
        const giftSelect = document.getElementById('giftImage');
        if (!giftSelect) return;

        // Clear existing options except the first one
        giftSelect.innerHTML = '<option value="" data-i18n="noGif">Không có</option>';
        
        // Add available GIFs from the gif folder
        const availableGifs = [
            './gif/Cat Love GIF by KIKI.gif',
            './image/happy.gif'
        ];

        availableGifs.forEach(gif => {
            const option = document.createElement('option');
            option.value = gif;
            option.textContent = gif.split('/').pop().replace(/\.[^/.]+$/, ""); // Remove extension
            giftSelect.appendChild(option);
        });
    }

    initializeBookPages() {
        const pageConfigs = document.getElementById('pageConfigs');
        if (!pageConfigs) return;

        // Clear existing pages
        pageConfigs.innerHTML = '';
        
        // Add default page if none exist
        const currentSettings = this.getCurrentSettings();
        if (!currentSettings.pages || currentSettings.pages.length === 0) {
            this.addPageConfiguration(0, this.defaultSettings.pages[0]);
        } else {
            currentSettings.pages.forEach((page, index) => {
                this.addPageConfiguration(index, page);
            });
        }
        
        this.setupAddPageButton();
    }

    setupAddPageButton() {
        const pageConfigs = document.getElementById('pageConfigs');
        if (!pageConfigs) return;

        // Remove existing add button
        const existingButton = pageConfigs.querySelector('.add-page-btn');
        if (existingButton) {
            existingButton.remove();
        }

        // Add new page button
        const addButton = document.createElement('button');
        addButton.type = 'button';
        addButton.className = 'add-page-btn';
        addButton.innerHTML = '➕ ' + t('addNewPage');
        addButton.style.cssText = `
            width: 100%;
            padding: 12px;
            margin-top: 15px;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
        `;
        
        addButton.addEventListener('click', () => {
            this.addNewPage();
        });
        
        addButton.addEventListener('mouseenter', () => {
            addButton.style.transform = 'translateY(-2px)';
            addButton.style.boxShadow = '0 4px 12px rgba(76, 175, 80, 0.3)';
        });
        
        addButton.addEventListener('mouseleave', () => {
            addButton.style.transform = 'translateY(0)';
            addButton.style.boxShadow = 'none';
        });

        pageConfigs.appendChild(addButton);
    }

    addNewPage() {
        const pageConfigs = document.getElementById('pageConfigs');
        if (!pageConfigs) return;

        const currentPages = pageConfigs.querySelectorAll('.page-config').length;
        const newPage = { content: '', image: '' };
        
        // Insert before the add button
        const addButton = pageConfigs.querySelector('.add-page-btn');
        const newPageElement = this.createPageConfigElement(currentPages, newPage);
        
        if (addButton) {
            pageConfigs.insertBefore(newPageElement, addButton);
        } else {
            pageConfigs.appendChild(newPageElement);
        }
    }

    addPageConfiguration(index, page = {}) {
        const pageConfigs = document.getElementById('pageConfigs');
        if (!pageConfigs) return;

        const pageElement = this.createPageConfigElement(index, page);
        pageConfigs.appendChild(pageElement);
    }

    createPageConfigElement(index, page) {
        const pageDiv = document.createElement('div');
        pageDiv.className = 'page-config';
        pageDiv.style.position = 'relative';
        
        const isFirstPage = index === 0;
        const pageTitle = isFirstPage ? t('pageTitleCover', {num: index + 1}) : t('pageTitle', {num: index + 1});
        
        pageDiv.innerHTML = `
            <button type="button" class="page-config-close" onclick="this.parentElement.remove(); window.settingsManager.updatePageNumbers();">×</button>
            <h3>${pageTitle}</h3>
            <div class="form-group">
                <label for="pageImage${index}">${t('imageLabel')}</label>
                <input type="file" id="pageImage${index}" accept="image/*" onchange="window.settingsManager.previewImage(this, ${index})">
                <div id="imagePreview${index}" style="margin-top: 10px;">
                    ${page.image ? `<img src="${page.image}" style="max-width: 200px; max-height: 150px; border-radius: 8px; object-fit: cover;">` : ''}
                </div>
            </div>
            <div class="form-group">
                <label for="pageContent${index}">${t('contentLabel')}</label>
                <textarea id="pageContent${index}" placeholder="${t('contentPlaceholder', {num: index + 1})}" rows="3">${page.content || ''}</textarea>
            </div>
        `;
        
        return pageDiv;
    }

    previewImage(input, index) {
        const preview = document.getElementById(`imagePreview${index}`);
        if (!preview) return;

        if (input.files && input.files[0]) {
            const reader = new FileReader();
            reader.onload = (e) => {
                preview.innerHTML = `<img src="${e.target.result}" style="max-width: 200px; max-height: 150px; border-radius: 8px; object-fit: cover;">`;
            };
            reader.readAsDataURL(input.files[0]);
        }
    }

    updatePageNumbers() {
        const pageConfigs = document.querySelectorAll('.page-config');
        pageConfigs.forEach((config, index) => {
            const title = config.querySelector('h3');
            const isFirstPage = index === 0;
            const pageTitle = isFirstPage ? t('pageTitleCover', {num: index + 1}) : t('pageTitle', {num: index + 1});
            if (title) {
                title.textContent = pageTitle;
            }
            
            // Update IDs and references
            const imageInput = config.querySelector('input[type="file"]');
            const textarea = config.querySelector('textarea');
            const preview = config.querySelector('[id^="imagePreview"]');
            
            if (imageInput) {
                imageInput.id = `pageImage${index}`;
                imageInput.setAttribute('onchange', `window.settingsManager.previewImage(this, ${index})`);
            }
            if (textarea) {
                textarea.id = `pageContent${index}`;
                textarea.placeholder = t('contentPlaceholder', {num: index + 1});
            }
            if (preview) {
                preview.id = `imagePreview${index}`;
            }
        });
    }

    getCurrentSettings() {
        const settings = {};
        
        // Get basic settings
        const elements = [
            'backgroundMusic', 'countdownTime', 'matrixText', 
            'matrixColor1', 'matrixColor2', 'sequenceText', 
            'sequenceColor', 'giftImage', 'enableBook', 'enableHeart'
        ];
        
        elements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                if (element.type === 'checkbox') {
                    settings[id] = element.checked;
                } else if (element.type === 'number') {
                    settings[id] = parseInt(element.value) || this.defaultSettings[id];
                } else if (id === 'enableBook' || id === 'enableHeart') {
                    settings[id] = element.value === 'true';
                } else {
                    settings[id] = element.value || this.defaultSettings[id];
                }
            }
        });

        // Get page settings
        settings.pages = this.getPageSettings();
        
        return settings;
    }

    getPageSettings() {
        const pages = [];
        const pageConfigs = document.querySelectorAll('.page-config');
        
        pageConfigs.forEach((config, index) => {
            const contentTextarea = config.querySelector('textarea');
            const imagePreview = config.querySelector('[id^="imagePreview"] img');
            const fileInput = config.querySelector('input[type="file"]');
            
            let imageData = '';
            if (imagePreview) {
                imageData = imagePreview.src;
            } else if (fileInput && fileInput.files && fileInput.files[0]) {
                // Handle file input if no preview exists yet
                const reader = new FileReader();
                reader.onload = (e) => {
                    imageData = e.target.result;
                };
                reader.readAsDataURL(fileInput.files[0]);
            }
            
            const page = {
                content: contentTextarea ? contentTextarea.value : '',
                image: imageData
            };
            
            // Only add non-empty pages
            if (page.content.trim() || page.image) {
                pages.push(page);
            }
        });
        
        return pages;
    }

    loadSettings(settings) {
        // Load basic settings
        Object.keys(settings).forEach(key => {
            const element = document.getElementById(key);
            if (element && key !== 'pages') {
                if (element.type === 'checkbox') {
                    element.checked = settings[key];
                } else if (key === 'enableBook' || key === 'enableHeart') {
                    element.value = settings[key] ? 'true' : 'false';
                } else {
                    element.value = settings[key];
                }
            }
        });

        // Load page settings
        if (settings.enableBook && settings.pages) {
            const bookSettingsSection = document.getElementById('bookSettingsSection');
            if (bookSettingsSection) {
                bookSettingsSection.style.display = 'block';
            }
            
            const pageConfigs = document.getElementById('pageConfigs');
            if (pageConfigs) {
                pageConfigs.innerHTML = '';
                settings.pages.forEach((page, index) => {
                    this.addPageConfiguration(index, page);
                });
                this.setupAddPageButton();
            }
        }
    }

    validateSettings(settings) {
        const errors = [];
        
        // Validate required fields
        if (!settings.sequenceText || settings.sequenceText.trim() === '') {
            errors.push('Sequence text is required');
        }
        
        if (settings.enableBook && (!settings.pages || settings.pages.length === 0)) {
            errors.push('At least one page is required when book is enabled');
        }
        
        if (settings.countdownTime < 1 || settings.countdownTime > 30) {
            errors.push('Countdown time must be between 1 and 30 seconds');
        }
        
        return errors;
    }

    resetToDefaults() {
        if (confirm('Are you sure you want to reset all settings to defaults?')) {
            this.loadSettings(this.defaultSettings);
        }
    }
}

// Initialize settings manager
document.addEventListener('DOMContentLoaded', () => {
    window.settingsManager = new SettingsManager();
});
