// Enhanced Book Animation System
class BookAnimation {
    constructor() {
        this.currentPage = 0;
        this.totalPages = 0;
        this.isAnimating = false;
        this.pages = [];
        this.book = null;
        this.autoFlipInterval = null;
        this.flipSound = null;
        
        this.init();
    }

    init() {
        this.book = document.getElementById('book');
        this.setupEventListeners();
        this.createFlipSound();
    }

    createFlipSound() {
        // Create a subtle page flip sound effect
        this.flipSound = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
    }

    setupEventListeners() {
        if (!this.book) return;

        // Click to flip pages
        this.book.addEventListener('click', (e) => {
            const page = e.target.closest('.page');
            if (page && !this.isAnimating) {
                const pageIndex = Array.from(this.book.children).indexOf(page);
                this.flipToPage(pageIndex);
            }
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (!this.book.classList.contains('show')) return;
            
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                    e.preventDefault();
                    this.nextPage();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    this.previousPage();
                    break;
                case 'Home':
                    e.preventDefault();
                    this.goToFirstPage();
                    break;
                case 'End':
                    e.preventDefault();
                    this.goToLastPage();
                    break;
            }
        });

        // Touch/swipe support for mobile
        this.setupTouchEvents();

        // Auto-flip feature
        this.setupAutoFlip();
    }

    setupTouchEvents() {
        let startX = 0;
        let startY = 0;
        let endX = 0;
        let endY = 0;

        this.book.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });

        this.book.addEventListener('touchend', (e) => {
            endX = e.changedTouches[0].clientX;
            endY = e.changedTouches[0].clientY;
            
            const deltaX = endX - startX;
            const deltaY = endY - startY;
            
            // Check if it's a horizontal swipe
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
                if (deltaX > 0) {
                    this.previousPage(); // Swipe right = previous page
                } else {
                    this.nextPage(); // Swipe left = next page
                }
            }
        });
    }

    setupAutoFlip() {
        // Auto-flip pages every 5 seconds when book is visible
        const startAutoFlip = () => {
            this.stopAutoFlip();
            this.autoFlipInterval = setInterval(() => {
                if (this.currentPage < this.totalPages - 1) {
                    this.nextPage();
                } else {
                    this.goToFirstPage();
                }
            }, 5000);
        };

        const stopAutoFlip = () => {
            this.stopAutoFlip();
        };

        // Start auto-flip when book becomes visible
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.target.classList.contains('show')) {
                    setTimeout(startAutoFlip, 2000); // Start after 2 seconds
                }
            });
        });

        if (this.book) {
            observer.observe(this.book, { attributes: true, attributeFilter: ['class'] });
        }

        // Stop auto-flip on user interaction
        this.book?.addEventListener('click', stopAutoFlip);
        this.book?.addEventListener('touchstart', stopAutoFlip);
        document.addEventListener('keydown', (e) => {
            if (['ArrowLeft', 'ArrowRight', ' ', 'Home', 'End'].includes(e.key)) {
                stopAutoFlip();
            }
        });
    }

    stopAutoFlip() {
        if (this.autoFlipInterval) {
            clearInterval(this.autoFlipInterval);
            this.autoFlipInterval = null;
        }
    }

    createPages(pagesData) {
        if (!this.book) return;

        this.book.innerHTML = '';
        this.pages = pagesData;
        this.totalPages = pagesData.length;
        this.currentPage = 0;

        pagesData.forEach((pageData, index) => {
            const pageElement = this.createPageElement(pageData, index);
            this.book.appendChild(pageElement);
        });

        this.updatePageZIndex();
    }

    createPageElement(pageData, index) {
        const page = document.createElement('div');
        page.className = 'page';
        page.setAttribute('data-page', index);
        
        // Create front side
        const frontSide = document.createElement('div');
        frontSide.className = 'page-front';
        
        // Create back side
        const backSide = document.createElement('div');
        backSide.className = 'page-back';
        
        // Add content to front side
        if (pageData.image) {
            const img = document.createElement('img');
            img.src = pageData.image;
            img.alt = pageData.content || `Page ${index + 1}`;
            img.style.cssText = `
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: inherit;
            `;
            frontSide.appendChild(img);
        }
        
        if (pageData.content) {
            const contentDiv = document.createElement('div');
            contentDiv.className = 'page-content';
            contentDiv.innerHTML = pageData.content;
            contentDiv.style.cssText = `
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                background: rgba(255, 255, 255, 0.9);
                padding: 15px;
                font-size: 14px;
                text-align: center;
                color: #333;
                border-radius: 0 0 8px 0;
            `;
            frontSide.appendChild(contentDiv);
        }

        // Add page number
        const pageNumber = document.createElement('div');
        pageNumber.className = 'page-number';
        pageNumber.textContent = index + 1;
        pageNumber.style.cssText = `
            position: absolute;
            bottom: 10px;
            right: 15px;
            font-size: 12px;
            color: #666;
            background: rgba(255, 255, 255, 0.8);
            padding: 2px 6px;
            border-radius: 3px;
            z-index: 10;
        `;
        frontSide.appendChild(pageNumber);

        // Add content to back side (next page preview or empty)
        if (index < this.pages.length - 1 && this.pages[index + 1]) {
            const nextPageData = this.pages[index + 1];
            if (nextPageData.image) {
                const img = document.createElement('img');
                img.src = nextPageData.image;
                img.alt = `Preview of page ${index + 2}`;
                img.style.cssText = `
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    border-radius: inherit;
                    opacity: 0.3;
                `;
                backSide.appendChild(img);
            }
        }

        page.appendChild(frontSide);
        page.appendChild(backSide);
        
        return page;
    }

    updatePageZIndex() {
        const pages = this.book.querySelectorAll('.page');
        pages.forEach((page, index) => {
            const isFlipped = page.classList.contains('flipped');
            if (isFlipped) {
                page.style.zIndex = index + 1;
            } else {
                page.style.zIndex = this.totalPages - index;
            }
        });
    }

    flipToPage(pageIndex) {
        if (this.isAnimating || pageIndex < 0 || pageIndex >= this.totalPages) return;

        const page = this.book.children[pageIndex];
        if (!page) return;

        this.flipPage(page, pageIndex);
    }

    flipPage(pageElement, pageIndex) {
        if (this.isAnimating) return;

        this.isAnimating = true;
        this.currentPage = pageIndex;

        // Play flip sound
        if (this.flipSound) {
            this.flipSound.currentTime = 0;
            this.flipSound.volume = 0.3;
            this.flipSound.play().catch(() => {}); // Ignore errors
        }

        // Add flipping class for intermediate animation
        pageElement.classList.add('flipping');

        // Create a subtle shake effect on the book
        this.book.style.transform = 'translateX(2px)';
        setTimeout(() => {
            this.book.style.transform = 'translateX(-2px)';
        }, 100);
        setTimeout(() => {
            this.book.style.transform = 'translateX(0)';
        }, 200);

        setTimeout(() => {
            pageElement.classList.remove('flipping');
            pageElement.classList.add('flipped');
            this.updatePageZIndex();
            this.isAnimating = false;

            // Trigger page flip event
            this.onPageFlip(pageIndex);
        }, 400);
    }

    nextPage() {
        if (this.currentPage < this.totalPages - 1) {
            const nextPageIndex = this.currentPage + 1;
            this.flipToPage(nextPageIndex);
        }
    }

    previousPage() {
        if (this.currentPage > 0) {
            const prevPageIndex = this.currentPage - 1;
            const page = this.book.children[this.currentPage];
            
            if (page && page.classList.contains('flipped')) {
                this.unflipPage(page, prevPageIndex);
            }
        }
    }

    unflipPage(pageElement, pageIndex) {
        if (this.isAnimating) return;

        this.isAnimating = true;
        this.currentPage = pageIndex;

        // Play flip sound
        if (this.flipSound) {
            this.flipSound.currentTime = 0;
            this.flipSound.volume = 0.3;
            this.flipSound.play().catch(() => {});
        }

        pageElement.classList.add('flipping');

        setTimeout(() => {
            pageElement.classList.remove('flipping');
            pageElement.classList.remove('flipped');
            this.updatePageZIndex();
            this.isAnimating = false;

            this.onPageFlip(pageIndex);
        }, 400);
    }

    goToFirstPage() {
        const pages = this.book.querySelectorAll('.page.flipped');
        pages.forEach(page => {
            page.classList.remove('flipped');
        });
        this.currentPage = 0;
        this.updatePageZIndex();
    }

    goToLastPage() {
        const pages = this.book.querySelectorAll('.page');
        pages.forEach(page => {
            page.classList.add('flipped');
        });
        this.currentPage = this.totalPages - 1;
        this.updatePageZIndex();
    }

    onPageFlip(pageIndex) {
        // Trigger custom event
        const event = new CustomEvent('pageFlip', {
            detail: { pageIndex, totalPages: this.totalPages }
        });
        document.dispatchEvent(event);

        // Update page indicator if exists
        this.updatePageIndicator();
    }

    updatePageIndicator() {
        // Create or update page indicator
        let indicator = document.getElementById('page-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'page-indicator';
            indicator.style.cssText = `
                position: fixed;
                bottom: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(0, 0, 0, 0.7);
                color: white;
                padding: 8px 16px;
                border-radius: 20px;
                font-size: 14px;
                z-index: 1000;
                transition: opacity 0.3s ease;
            `;
            document.body.appendChild(indicator);
        }

        indicator.textContent = `${this.currentPage + 1} / ${this.totalPages}`;
        indicator.style.opacity = '1';

        // Hide after 2 seconds
        setTimeout(() => {
            indicator.style.opacity = '0';
        }, 2000);
    }

    destroy() {
        this.stopAutoFlip();
        if (this.book) {
            this.book.innerHTML = '';
        }
    }
}

// Initialize book animation
document.addEventListener('DOMContentLoaded', () => {
    window.bookAnimation = new BookAnimation();
});
