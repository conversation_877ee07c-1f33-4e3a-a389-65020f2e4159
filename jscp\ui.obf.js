// Placeholder for ui.obf.js
const a0_0x360877 = a0_0x48de;
(function(_0x31de54, _0x4041d6) {
    const _0x518c60 = a0_0x48de
      , _0x2a0679 = _0x31de54();
    while (!![]) {
        try {
            const _0x36faa2 = parseInt(_0x518c60(0x102)) / 0x1 * (parseInt(_0x518c60(0x19c)) / 0x2) + parseInt(_0x518c60(0x164)) / 0x3 * (-parseInt(_0x518c60(0x10a)) / 0x4) + parseInt(_0x518c60(0x14b)) / 0x5 + -parseInt(_0x518c60(0x165)) / 0x6 * (-parseInt(_0x518c60(0x1ca)) / 0x7) + -parseInt(_0x518c60(0xf7)) / 0x8 + parseInt(_0x518c60(0x14a)) / 0x9 * (parseInt(_0x518c60(0x142)) / 0xa) + -parseInt(_0x518c60(0x109)) / 0xb;
            if (_0x36faa2 === _0x4041d6)
                break;
            else
                _0x2a0679['push'](_0x2a0679['shift']());
        } catch (_0x543514) {
            _0x2a0679['push'](_0x2a0679['shift']());
        }
    }
}(a0_0x4c4a, 0xd4e63));
let isLandscape = ![]
  , matrixInterval = null;
const confettiPool = []
  , maxConfetti = 0x32
  , isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i[a0_0x360877(0xed)](navigator[a0_0x360877(0x159)]);
function createConfetti() {
    const _0x7f6620 = a0_0x360877
      , _0x385ca1 = document[_0x7f6620(0x135)](_0x7f6620(0x1ae));
    return _0x385ca1[_0x7f6620(0x1b4)] = _0x7f6620(0x1bf),
    _0x385ca1;
}
function getConfettiFromPool() {
    const _0x2f8398 = a0_0x360877;
    if (confettiPool[_0x2f8398(0x18e)] > 0x0)
        return confettiPool[_0x2f8398(0x14e)]();
    return createConfetti();
}
function forceResizeMatrix() {
    const _0x4c34c1 = a0_0x360877
      , _0x194b26 = document['getElementById'](_0x4c34c1(0x194));
    _0x194b26 && (_0x194b26[_0x4c34c1(0x174)] = window[_0x4c34c1(0x1ce)],
    _0x194b26['height'] = window[_0x4c34c1(0x12e)],
    matrixInterval && (clearInterval(matrixInterval),
    matrixInterval = null),
    initMatrixRain());
}
function a0_0x4c4a() {
    const _0x48537a = ['medium', 'clientHeight', 'visibilitychange', 'length', 'starsContainer', 'concat', 'changedTouches', 'flipping', 'book', 'matrix-rain', 'exec', 'arc', 'beginPath', 'animation', '_update', 'Avenir,\x20Helvetica\x20Neue,\x20Helvetica,\x20Arial,\x20sans-serif', 'split', '256252LFKElc', 'HAPPYBIRTHDAY', '_draw', 'fontSize', 'touches', 'getContext', 'log', 'Shape', 'call', 'contentDisplay', 'play', 'enableHeart', 'getHours', 'playing', 'catch', 'body', 'sin', 'backgroundColor', 'div', 'charAt', 'matches', 'preventDefault', 'ended', 'boxShadow', 'className', 'heart', 'matrixColor1', 'contains', '\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<svg\x20xmlns=\x22http://www.w3.org/2000/svg\x22\x20width=\x22300\x22\x20height=\x22400\x22\x20viewBox=\x220\x200\x20300\x20400\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<rect\x20width=\x22300\x22\x20height=\x22400\x22\x20fill=\x22#f0f0f0\x22/>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<text\x20x=\x22150\x22\x20y=\x22200\x22\x20text-anchor=\x22middle\x22\x20font-family=\x22Arial\x22\x20font-size=\x2216\x22\x20fill=\x22#999\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20', 'transition', 'font', 'DOMContentLoaded', '--x', 'shift', 'remove', 'confetti', 'slice', 'render', 'push', 'filter', 'maxSize', '%;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20top:\x20', 'scrollTop', 'translate(-50%,\x20-50%)\x20scale(0)', 'rgba(0,\x200,\x200,\x200.05)', 'fill', '91siuHAj', 'Canvas\x20not\x20initialized,\x20returning\x20default\x20area', 'init', 'fillText', 'innerWidth', 'src', 'all\x201.5s\x20ease-out', 'closest', 'deg)\x20translateY(-40px)', 'title', 'small', 'flipped', '(orientation:\x20landscape)', 'test', 'rotateY(', 'middle', 'transform', 'href', 'img', 'adjustCanvas', 'classList', '|\x20Delay:', 'matchMedia', '4571384fRzWos', 'then', '.canvas', 'initialized', 'touchstart', 'bold\x20', '.page', 'settings', 'includes', 'clearFrame', 'rgba(', '4ejetVx', 'clone', '#ff3c78', 'textBaseline', 'image', 'distanceTo', 'createDocumentFragment', '397540EXmLsr', '6540iUOjfb', 'keydown', 'shadowBlur', 'opacity', 'letter', 'px\x20', 'content', 'minZ', '#ffc75f', 'mouseleave', 'appendChild', 'get', 'mousemove', 'minSize', 'ArrowRight', 'birthdayAudio', 'map', 'mouseup', 'cos', '\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20left:\x20', 'mozRequestAnimationFrame', 'matrixResizeTimeout', 'mousedown', 'contextmenu', 'What?', 'scrollHeight', 'floor', 'display', 'requestAnimationFrame', '\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</text>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</svg>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20', 'fillRect', 'string', '_moveTowards', 'forEach', 'Pause\x20Music', 'pow', 'innerHeight', 'touchend', 'volume', 'msRequestAnimationFrame', 'innerHTML', 'oRequestAnimationFrame', 'circle', 'createElement', 'touchmove', 'left', 'flex', 'dots', 'function', 'Point', 'style', 'sequenceColor', 'orientation-lock', 'Drawing', 'loop', 'ShapeBuilder', '190JRjtbn', 'addEventListener', 'warn', 'abs', 'setTimeout', 'block', '.book-container', 'opacity\x200.8s\x20ease,\x20transform\x200.8s\x20ease', '223551tqVNXO', '990100ezyVnr', 'getDotSize', 'switchShape', 'pop', 'cssText', 'max', 'scale(1)\x20translateY(0)', 'location', 'data:image/svg+xml;base64,', 'resize', 'querySelectorAll', '.page[data-page=\x22', 's;\x0a\x20\x20\x20\x20\x20\x20\x20\x20', 'min', 'userAgent', 'indexOf', '#ff6f91', 'trim', 'matrixColor2', 'pause', 'height', 'Color', 'random', 'set', 'musicControl', '2631zExGRH', '798936bfkxxz', 'photo', 'scale(0.8)\x20translateY(50px)', 'clear', 'key', 'show', 'zIndex', 'sqrt', 's;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20animation-delay:\x20', 'querySelector', 'clientX', 'move', '--y', 'click', 'textAlign', 'width', 'getElementById', 'undefined', 'now', 'top', '.content-display', 'clientY', 'webkitRequestAnimationFrame', 'bind', 'hidden', 'none', 'gift', 'ArrowLeft', 'px\x2010px\x2020px\x20rgba(0,0,0,', 'No\x20action\x20found\x20in\x20URL,\x20using\x20default\x20sequence.', 'setProperty', 'fillStyle', 'clearRect', 'rotate(', '#f9f871', 'closePath', 'add', 'red'];
    a0_0x4c4a = function() {
        return _0x48537a;
    }
    ;
    return a0_0x4c4a();
}
function returnConfettiToPool(_0x146ad8) {
    const _0x55512d = a0_0x360877;
    _0x146ad8[_0x55512d(0x1be)](),
    confettiPool[_0x55512d(0x1c2)](_0x146ad8);
}
function checkOrientation() {
    const _0x3defe0 = a0_0x360877
      , _0x260908 = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i[_0x3defe0(0xed)](navigator[_0x3defe0(0x159)])
      , _0x4c1c9b = document[_0x3defe0(0x175)](_0x3defe0(0x13e))
      , _0x1fc064 = document[_0x3defe0(0x175)](_0x3defe0(0x194))
      , _0x529d07 = document[_0x3defe0(0x16e)](_0x3defe0(0xf9))
      , _0x3da5ad = document[_0x3defe0(0x16e)]('.book-container')
      , _0x20760a = document[_0x3defe0(0x175)](_0x3defe0(0x193));
    if (!_0x260908) {
        isLandscape = !![],
        _0x4c1c9b[_0x3defe0(0x13c)][_0x3defe0(0x125)] = 'none',
        _0x1fc064['style']['display'] = _0x3defe0(0x147),
        _0x529d07['style'][_0x3defe0(0x125)] = 'block';
        if (_0x3da5ad)
            _0x3da5ad['style'][_0x3defe0(0x125)] = _0x3defe0(0x147);
        if (_0x20760a)
            _0x20760a[_0x3defe0(0x13c)][_0x3defe0(0x125)] = _0x3defe0(0x147);
        startWebsite();
    } else {
        const _0x3fb065 = window[_0x3defe0(0xf6)](_0x3defe0(0xec));
        isLandscape = _0x3fb065['matches'];
        if (isLandscape) {
            _0x4c1c9b[_0x3defe0(0x13c)][_0x3defe0(0x125)] = 'none',
            _0x1fc064[_0x3defe0(0x13c)]['display'] = _0x3defe0(0x147),
            _0x529d07['style']['display'] = _0x3defe0(0x147);
            if (_0x3da5ad)
                _0x3da5ad[_0x3defe0(0x13c)][_0x3defe0(0x125)] = 'block';
            if (_0x20760a)
                _0x20760a[_0x3defe0(0x13c)][_0x3defe0(0x125)] = _0x3defe0(0x147);
            startWebsite(),
            setTimeout( () => {
                forceResizeMatrix();
            }
            , 0x64);
        } else {
            _0x4c1c9b['style'][_0x3defe0(0x125)] = _0x3defe0(0x138),
            _0x1fc064[_0x3defe0(0x13c)][_0x3defe0(0x125)] = _0x3defe0(0x17e),
            _0x529d07[_0x3defe0(0x13c)][_0x3defe0(0x125)] = _0x3defe0(0x17e);
            if (_0x3da5ad)
                _0x3da5ad[_0x3defe0(0x13c)][_0x3defe0(0x125)] = 'none';
            if (_0x20760a)
                _0x20760a[_0x3defe0(0x13c)][_0x3defe0(0x125)] = 'none';
            stopWebsite();
        }
        _0x3fb065[_0x3defe0(0x143)]('change', _0x246a1d => {
            const _0x414fae = _0x3defe0;
            isLandscape = _0x246a1d[_0x414fae(0x1b0)];
            if (isLandscape) {
                _0x4c1c9b['style']['display'] = _0x414fae(0x17e),
                _0x1fc064['style'][_0x414fae(0x125)] = _0x414fae(0x147),
                _0x529d07['style'][_0x414fae(0x125)] = _0x414fae(0x147);
                if (_0x3da5ad)
                    _0x3da5ad[_0x414fae(0x13c)]['display'] = _0x414fae(0x147);
                if (_0x20760a)
                    _0x20760a['style'][_0x414fae(0x125)] = 'block';
                startWebsite(),
                setTimeout( () => {
                    forceResizeMatrix();
                }
                , 0x64);
            } else {
                _0x4c1c9b[_0x414fae(0x13c)][_0x414fae(0x125)] = _0x414fae(0x138),
                _0x1fc064['style']['display'] = _0x414fae(0x17e),
                _0x529d07[_0x414fae(0x13c)][_0x414fae(0x125)] = _0x414fae(0x17e);
                if (_0x3da5ad)
                    _0x3da5ad['style'][_0x414fae(0x125)] = _0x414fae(0x17e);
                if (_0x20760a)
                    _0x20760a[_0x414fae(0x13c)][_0x414fae(0x125)] = 'none';
                stopWebsite();
            }
        }
        );
    }
}
function startWebsite() {
    const _0x13aa3d = a0_0x360877;
    !matrixInterval && initMatrixRain(),
    typeof resetWebsiteState === _0x13aa3d(0x13a) && resetWebsiteState(),
    S[_0x13aa3d(0x1cc)](),
    S[_0x13aa3d(0xfa)] = !![];
}
function stopWebsite() {
    const _0x17e3a9 = a0_0x360877;
    if (matrixInterval) {
        clearInterval(matrixInterval),
        matrixInterval = null;
        const _0x2f4933 = document[_0x17e3a9(0x175)](_0x17e3a9(0x194));
        if (_0x2f4933) {
            const _0x1110cf = _0x2f4933[_0x17e3a9(0x1a1)]('2d');
            _0x1110cf[_0x17e3a9(0x185)](0x0, 0x0, _0x2f4933[_0x17e3a9(0x174)], _0x2f4933['height']);
        }
    }
}
let matrixChars = a0_0x360877(0x19d)[a0_0x360877(0x19b)]('');
function initMatrixRain() {
    const _0x29acbf = a0_0x360877
      , _0x62cd59 = document[_0x29acbf(0x175)](_0x29acbf(0x194))
      , _0x3c1a9c = _0x62cd59[_0x29acbf(0x1a1)]('2d');
    _0x62cd59['width'] = window[_0x29acbf(0x1ce)],
    _0x62cd59[_0x29acbf(0x15f)] = window[_0x29acbf(0x12e)];
    const _0x13606e = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i[_0x29acbf(0xed)](navigator[_0x29acbf(0x159)])
      , _0xd5948a = _0x13606e ? 0xd : 0x19
      , _0x47ab23 = _0x13606e ? 0x2c : 0x32
      , _0x26d65d = Math[_0x29acbf(0x124)](_0x62cd59[_0x29acbf(0x174)] / _0xd5948a)
      , _0x1a8e20 = []
      , _0x5abf93 = []
      , _0x342dd6 = []
      , _0xd60c56 = []
      , _0x2e9646 = Math[_0x29acbf(0x124)](_0x62cd59['height'] / _0xd5948a) + 0x2;
    for (let _0x4d0f74 = 0x0; _0x4d0f74 < _0x26d65d; _0x4d0f74++) {
        _0x1a8e20[_0x4d0f74] = 0x0,
        _0x5abf93[_0x4d0f74] = _0x4d0f74 % 0x2 === 0x0 ? window['settings'] ? window[_0x29acbf(0xfe)]['matrixColor1'] : settings[_0x29acbf(0x1b6)] : window[_0x29acbf(0xfe)] ? window[_0x29acbf(0xfe)][_0x29acbf(0x15d)] : settings[_0x29acbf(0x15d)],
        _0x342dd6[_0x4d0f74] = Math[_0x29acbf(0x161)]() * 0x7d0,
        _0xd60c56[_0x4d0f74] = ![];
    }
    let _0x21741e = Date[_0x29acbf(0x177)]();
    function _0x2ce951() {
        const _0x153e97 = _0x29acbf;
        _0x3c1a9c[_0x153e97(0x184)] = _0x153e97(0x1c8),
        _0x3c1a9c[_0x153e97(0x128)](0x0, 0x0, _0x62cd59[_0x153e97(0x174)], _0x62cd59[_0x153e97(0x15f)]),
        _0x3c1a9c[_0x153e97(0x1ba)] = _0x153e97(0xfc) + _0xd5948a + 'px\x20Menlo,\x20Consolas,\x20\x27Liberation\x20Mono\x27,\x20\x27Courier\x20New\x27,\x20monospace';
        const _0x1bf7ea = Date[_0x153e97(0x177)]();
        for (let _0x38dd7f = 0x0; _0x38dd7f < _0x1a8e20[_0x153e97(0x18e)]; _0x38dd7f++) {
            !_0xd60c56[_0x38dd7f] && _0x1bf7ea - _0x21741e >= _0x342dd6[_0x38dd7f] && (_0xd60c56[_0x38dd7f] = !![]);
            if (_0xd60c56[_0x38dd7f] && _0x1a8e20[_0x38dd7f] < _0x2e9646) {
                const _0x23185e = matrixChars[Math['floor'](Math[_0x153e97(0x161)]() * matrixChars['length'])]
                  , _0x1abaf2 = _0x38dd7f * _0xd5948a
                  , _0x9e0f73 = _0x1a8e20[_0x38dd7f] * _0xd5948a
                  , _0x1eeeb2 = _0x5abf93[_0x38dd7f];
                _0x3c1a9c[_0x153e97(0x184)] = _0x1eeeb2,
                _0x3c1a9c['shadowColor'] = _0x1eeeb2,
                _0x3c1a9c[_0x153e97(0x10c)] = 0x8,
                _0x3c1a9c[_0x153e97(0x1cd)](_0x23185e, _0x1abaf2, _0x9e0f73),
                _0x3c1a9c[_0x153e97(0x10c)] = 0x0;
            }
            _0xd60c56[_0x38dd7f] && _0x1a8e20[_0x38dd7f]++,
            _0x1a8e20[_0x38dd7f] >= _0x2e9646 && (_0x1a8e20[_0x38dd7f] = 0x0,
            _0x342dd6[_0x38dd7f] = Math['random']() * 0x3e8,
            _0xd60c56[_0x38dd7f] = ![]);
        }
    }
    matrixInterval = setInterval(_0x2ce951, _0x47ab23),
    window[_0x29acbf(0x143)](_0x29acbf(0x154), () => {
        const _0x4a4568 = _0x29acbf;
        clearTimeout(window[_0x4a4568(0x11f)]),
        window['matrixResizeTimeout'] = setTimeout( () => {
            const _0x372261 = _0x4a4568;
            _0x62cd59['width'] = window[_0x372261(0x1ce)],
            _0x62cd59[_0x372261(0x15f)] = window[_0x372261(0x12e)];
            const _0xf5f908 = Math[_0x372261(0x124)](_0x62cd59['width'] / _0xd5948a)
              , _0x4e2efa = Math[_0x372261(0x124)](_0x62cd59[_0x372261(0x15f)] / _0xd5948a) + 0x2;
            _0x1a8e20[_0x372261(0x18e)] = 0x0,
            _0x5abf93[_0x372261(0x18e)] = 0x0,
            _0x342dd6[_0x372261(0x18e)] = 0x0,
            _0xd60c56[_0x372261(0x18e)] = 0x0;
            for (let _0x54da70 = 0x0; _0x54da70 < _0xf5f908; _0x54da70++) {
                _0x1a8e20[_0x54da70] = 0x0,
                _0x5abf93[_0x54da70] = _0x54da70 % 0x2 === 0x0 ? window[_0x372261(0xfe)] ? window[_0x372261(0xfe)]['matrixColor1'] : settings[_0x372261(0x1b6)] : window[_0x372261(0xfe)] ? window[_0x372261(0xfe)][_0x372261(0x15d)] : settings['matrixColor2'],
                _0x342dd6[_0x54da70] = Math[_0x372261(0x161)]() * 0x3e8,
                _0xd60c56[_0x54da70] = ![];
            }
            _0x21741e = Date[_0x372261(0x177)]();
        }
        , 0x64);
    }
    );
}
S = {
    'initialized': ![],
    'init': function() {
        const _0x1a39d9 = a0_0x360877;
        if (!isLandscape && /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i[_0x1a39d9(0xed)](navigator[_0x1a39d9(0x159)]))
            return;
        var _0x2b8dd9 = window[_0x1a39d9(0x152)][_0x1a39d9(0xf1)]
          , _0x234817 = _0x2b8dd9[_0x1a39d9(0x15a)]('?websiteId=');
        if (_0x234817 !== -0x1) {} else
            console['log'](_0x1a39d9(0x182));
        S[_0x1a39d9(0x13f)][_0x1a39d9(0x1cc)](_0x1a39d9(0xf9)),
        document[_0x1a39d9(0x1ab)]['classList'][_0x1a39d9(0x189)]('body--ready'),
        S[_0x1a39d9(0x13f)][_0x1a39d9(0x140)](function() {
            const _0x203720 = _0x1a39d9;
            S[_0x203720(0x1a3)][_0x203720(0x1c1)]();
        });
    }
},
document[a0_0x360877(0x143)](a0_0x360877(0x1bb), checkOrientation),
S['Drawing'] = (function() {
    const _0x769be8 = a0_0x360877;
    var _0x36f13d, _0x2ed755, _0x251a27, _0x467d60 = window[_0x769be8(0x126)] || window[_0x769be8(0x17b)] || window[_0x769be8(0x11e)] || window[_0x769be8(0x133)] || window[_0x769be8(0x131)] || function(_0x1c0181) {
        const _0x35c841 = _0x769be8;
        window[_0x35c841(0x146)](_0x1c0181, 0x3e8 / 0x3c);
    }
    ;
    return {
        'init': function(_0xe7b766) {
            const _0x5021f5 = _0x769be8;
            _0x36f13d = document[_0x5021f5(0x16e)](_0xe7b766),
            _0x2ed755 = _0x36f13d['getContext']('2d'),
            this[_0x5021f5(0xf3)](),
            window[_0x5021f5(0x143)](_0x5021f5(0x154), function() {
                const _0x4c7d55 = _0x5021f5;
                S[_0x4c7d55(0x13f)]['adjustCanvas']();
            });
        },
        'loop': function(_0x44f1ce) {
            const _0x2c8d0a = _0x769be8;
            _0x251a27 = !_0x251a27 ? _0x44f1ce : _0x251a27,
            this[_0x2c8d0a(0x100)](),
            _0x251a27(),
            _0x467d60[_0x2c8d0a(0x1a4)](window, this[_0x2c8d0a(0x140)][_0x2c8d0a(0x17c)](this));
        },
        'adjustCanvas': function() {
            const _0x50f96f = _0x769be8;
            _0x36f13d[_0x50f96f(0x174)] = window['innerWidth'],
            _0x36f13d[_0x50f96f(0x15f)] = window[_0x50f96f(0x12e)];
        },
        'clearFrame': function() {
            const _0x62b06f = _0x769be8;
            _0x2ed755['clearRect'](0x0, 0x0, _0x36f13d[_0x62b06f(0x174)], _0x36f13d[_0x62b06f(0x15f)]);
        },
        'getArea': function() {
            const _0x130c30 = _0x769be8;
            if (!_0x36f13d)
                return console[_0x130c30(0x144)](_0x130c30(0x1cb)),
                {
                    'w': window['innerWidth'] || 0x320,
                    'h': window['innerHeight'] || 0x258
                };
            return {
                'w': _0x36f13d[_0x130c30(0x174)],
                'h': _0x36f13d['height']
            };
        },
        'drawCircle': function(_0x53b69f, _0x18f115) {
            const _0x530e4d = _0x769be8;
            _0x2ed755[_0x530e4d(0x184)] = _0x18f115[_0x530e4d(0x1c1)](),
            _0x2ed755[_0x530e4d(0x197)](),
            _0x2ed755[_0x530e4d(0x196)](_0x53b69f['x'], _0x53b69f['y'], _0x53b69f['z'], 0x0, 0x2 * Math['PI'], !![]),
            _0x2ed755[_0x530e4d(0x188)](),
            _0x2ed755[_0x530e4d(0x1c9)]();
        }
    };
}()),
S['UI'] = (function() {
    const _0x16befe = a0_0x360877;
    var _0x10ac10 = document[_0x16befe(0x16e)](_0x16befe(0xf9)), _0x73cbab, _0x15acfd, _0x1b6e98, _0x35eb53 = 0x1e, _0x1a5aa2 = !![], _0x145664 = [], _0x5d5219 = '#';
    function _0x1de6d3(_0x2e8b36) {
        const _0x13e441 = _0x16befe;
        var _0x396cd6 = _0x2e8b36[_0x13e441(0x1a8)]()
          , _0x1bb556 = _0x2e8b36['getMinutes']()
          , _0x1bb556 = _0x1bb556 < 0xa ? '0' + _0x1bb556 : _0x1bb556;
        return _0x396cd6 + ':' + _0x1bb556;
    }
    function _0x370fb6(_0x13a0bc) {
        return _0x13a0bc && _0x13a0bc['split']('\x20')[0x1];
    }
    function _0x3f8167(_0x383607) {
        const _0x30d0cb = _0x16befe;
        return _0x383607 = _0x383607 && _0x383607[_0x30d0cb(0x19b)]('\x20')[0x0],
        _0x383607 && _0x383607[0x0] === _0x5d5219 && _0x383607['substring'](0x1);
    }
    function _0x328855(_0x1cf0ba, _0x483a3f, _0x4152f5, _0x5234bb) {
        clearInterval(_0x73cbab),
        _0x15acfd = _0x5234bb ? _0x4152f5 : 0x1,
        _0x1cf0ba(_0x15acfd),
        (!_0x4152f5 || !_0x5234bb && _0x15acfd < _0x4152f5 || _0x5234bb && _0x15acfd > 0x0) && (_0x73cbab = setInterval(function() {
            _0x15acfd = _0x5234bb ? _0x15acfd - 0x1 : _0x15acfd + 0x1,
            _0x1cf0ba(_0x15acfd),
            (!_0x5234bb && _0x4152f5 && _0x15acfd === _0x4152f5 || _0x5234bb && _0x15acfd === 0x0) && clearInterval(_0x73cbab);
        }, _0x483a3f));
    }
    function _0x84847(_0x5d0a66) {
        const _0x5eff03 = _0x16befe;
        clearInterval(_0x73cbab),
        _0x145664 = [],
        _0x1b6e98 = null,
        _0x5d0a66 && S['Shape'][_0x5eff03(0x14d)](S['ShapeBuilder'][_0x5eff03(0x10e)](''));
    }
    function _0x5220da(_0x2ec14d) {
        const _0x23b875 = _0x16befe;
        var _0x2cac32, _0x2ec14d, _0x2a16c5;
        _0x145664 = typeof _0x2ec14d === 'object' ? _0x2ec14d : _0x145664[_0x23b875(0x190)](_0x2ec14d[_0x23b875(0x19b)]('|'));
        function _0x4e71f5(_0x435aae) {
            const _0x4b8d9d = _0x23b875
              , _0x10b9a8 = isMobile ? 0x6a4 : 0x76c;
            if (!_0x435aae || typeof _0x435aae !== _0x4b8d9d(0x129))
                return _0x10b9a8;
            if (_0x435aae[_0x4b8d9d(0x15c)]()['startsWith']('#'))
                return _0x10b9a8;
            const _0x4e5b42 = Math[_0x4b8d9d(0x150)](0x0, (_0x435aae[_0x4b8d9d(0x18e)] - 0x5) * 0x64);
            return _0x4e5b42 > 0x0 && console[_0x4b8d9d(0x1a2)]('Từ\x20được\x20thêm\x20thời\x20gian:', _0x435aae, _0x4b8d9d(0xf5), _0x10b9a8 + _0x4e5b42, 'ms'),
            _0x10b9a8 + _0x4e5b42;
        }
        _0x328855(function(_0xf699e) {
            const _0x213831 = _0x23b875;
            _0x2a16c5 = _0x145664[_0x213831(0x1bd)](),
            _0x2cac32 = _0x3f8167(_0x2a16c5),
            _0x2ec14d = _0x370fb6(_0x2a16c5);
            const _0xed29fe = _0x4e71f5(_0x2a16c5);
            switch (_0x2cac32) {
            case 'countdown':
                _0x2ec14d = parseInt(_0x2ec14d) || 0xa,
                _0x2ec14d = _0x2ec14d > 0x0 ? _0x2ec14d : 0xa,
                _0x328855(function(_0x41f446) {
                    const _0x10ec3e = _0x213831;
                    _0x41f446 === 0x0 ? _0x145664[_0x10ec3e(0x18e)] === 0x0 ? S[_0x10ec3e(0x1a3)]['switchShape'](S[_0x10ec3e(0x141)][_0x10ec3e(0x10e)]('')) : _0x5220da(_0x145664) : S[_0x10ec3e(0x1a3)][_0x10ec3e(0x14d)](S['ShapeBuilder'][_0x10ec3e(0x10e)](_0x41f446), !![]);
                }, isMobile ? 0x514 : 0x578, _0x2ec14d, !![]);
                break;
            case 'circle':
                _0x2ec14d = parseInt(_0x2ec14d) || _0x35eb53,
                _0x2ec14d = Math[_0x213831(0x158)](_0x2ec14d, _0x35eb53),
                S[_0x213831(0x1a3)][_0x213831(0x14d)](S[_0x213831(0x141)][_0x213831(0x134)](_0x2ec14d));
                break;
            case 'time':
                var _0x5ae29d = _0x1de6d3(new Date());
                _0x145664['length'] > 0x0 ? S[_0x213831(0x1a3)][_0x213831(0x14d)](S[_0x213831(0x141)][_0x213831(0x10e)](_0x5ae29d)) : _0x328855(function() {
                    const _0x2c9469 = _0x213831;
                    _0x5ae29d = _0x1de6d3(new Date()),
                    _0x5ae29d !== _0x1b6e98 && (_0x1b6e98 = _0x5ae29d,
                    S[_0x2c9469(0x1a3)][_0x2c9469(0x14d)](S[_0x2c9469(0x141)]['letter'](_0x1b6e98)));
                }, 0x3e8);
                break;
            case _0x213831(0x17f):
                const _0x3e21ec = document[_0x213831(0x16e)](_0x213831(0xf9))
                  , _0x5b5a5f = document['getElementById']('gift-image')
                  , _0x1d1c71 = document[_0x213831(0x175)]('matrix-rain');
                showStars(),
                showFloatingHearts();
                const _0x49f6e2 = window[_0x213831(0xfe)] || settings;
                _0x49f6e2['enableBook'] === !![] ? _0x3e21ec && _0x5b5a5f && _0x1d1c71 ? (_0x3e21ec[_0x213831(0x13c)]['display'] = _0x213831(0x17e),
                _0x1d1c71['style'][_0x213831(0x125)] = 'none',
                _0x5b5a5f[_0x213831(0x1cf)] && _0x5b5a5f[_0x213831(0x1cf)] !== window[_0x213831(0x152)]['href'] && _0x5b5a5f[_0x213831(0x1cf)] !== '' && !_0x5b5a5f['src'][_0x213831(0xff)](_0x213831(0x176)) ? (_0x5b5a5f[_0x213831(0x13c)]['display'] = 'block',
                _0x5b5a5f['style'][_0x213831(0x198)] = 'giftCelebration\x202s\x20ease-in-out',
                setTimeout( () => {
                    const _0x1a1338 = _0x213831;
                    _0x5b5a5f['style'][_0x1a1338(0x125)] = _0x1a1338(0x17e),
                    showBook();
                }
                , 0xbb8)) : showBook()) : showBook() : (_0x3e21ec && _0x1d1c71 && (_0x3e21ec[_0x213831(0x13c)][_0x213831(0x125)] = _0x213831(0x17e),
                _0x1d1c71[_0x213831(0x13c)]['display'] = _0x213831(0x17e)),
                _0x5b5a5f && _0x5b5a5f[_0x213831(0x1cf)] && _0x5b5a5f[_0x213831(0x1cf)] !== window['location'][_0x213831(0xf1)] && _0x5b5a5f[_0x213831(0x1cf)] !== '' && !_0x5b5a5f[_0x213831(0x1cf)][_0x213831(0xff)](_0x213831(0x176)) ? (_0x5b5a5f[_0x213831(0x13c)][_0x213831(0x125)] = _0x213831(0x147),
                _0x5b5a5f[_0x213831(0x13c)][_0x213831(0x198)] = 'giftCelebration\x202s\x20ease-in-out',
                _0x49f6e2[_0x213831(0x1a7)] === !![] && setTimeout( () => {
                    startHeartEffect();
                }
                , 0x7d0)) : _0x49f6e2['enableHeart'] === !![] && startHeartEffect());
                break;
            default:
                S[_0x213831(0x1a3)][_0x213831(0x14d)](S[_0x213831(0x141)][_0x213831(0x10e)](_0x2a16c5[0x0] === _0x5d5219 ? _0x213831(0x122) : _0x2a16c5));
            }
        }, _0x4e71f5(_0x145664[0x0]), _0x145664[_0x23b875(0x18e)]);
    }
    function _0x2d8e6a() {
        const _0x4e5188 = _0x16befe;
        _0x10ac10[_0x4e5188(0x143)](_0x4e5188(0x172), function(_0x5843c8) {});
    }
    function _0x3ad9d8() {
        _0x2d8e6a();
    }
    return _0x3ad9d8(),
    {
        'simulate': function(_0x1f62cd) {
            const _0x58a8aa = _0x16befe;
            (isLandscape || !/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i[_0x58a8aa(0xed)](navigator['userAgent'])) && _0x5220da(_0x1f62cd);
        },
        'reset': function(_0x2eddc1) {
            _0x84847(_0x2eddc1);
        }
    };
}()),
S['Point'] = function(_0x37c98b) {
    this['x'] = _0x37c98b['x'],
    this['y'] = _0x37c98b['y'],
    this['z'] = _0x37c98b['z'],
    this['a'] = _0x37c98b['a'],
    this['h'] = _0x37c98b['h'];
}
,
S[a0_0x360877(0x160)] = function(_0x27ac7d, _0x4faa35, _0x5ebb7a, _0xeae705) {
    this['r'] = _0x27ac7d,
    this['g'] = _0x4faa35,
    this['b'] = _0x5ebb7a,
    this['a'] = _0xeae705;
}
,
S[a0_0x360877(0x160)]['prototype'] = {
    'render': function() {
        const _0x4b59d2 = a0_0x360877;
        return _0x4b59d2(0x101) + this['r'] + ',' + this['g'] + ',' + this['b'] + ',' + this['a'] + ')';
    }
},
S['Dot'] = function(_0xc2a8a2, _0x44976d) {
    const _0x1d2c99 = a0_0x360877;
    this['p'] = new S[(_0x1d2c99(0x13b))]({
        'x': _0xc2a8a2,
        'y': _0x44976d,
        'z': this[_0x1d2c99(0x14c)](),
        'a': 0x1,
        'h': 0x0
    }),
    this['e'] = 0.07,
    this['s'] = !![];
    const _0x4d8816 = window[_0x1d2c99(0xfe)] || settings
      , _0x47fe32 = hexToRgb(_0x4d8816['sequenceColor']);
    this['c'] = new S[(_0x1d2c99(0x160))](_0x47fe32['r'],_0x47fe32['g'],_0x47fe32['b'],this['p']['a']),
    this['t'] = this[_0x1d2c99(0x103)](),
    this['q'] = [];
}
,
S['Dot']['prototype'] = {
    'getDotSize': function() {
        const _0x38db90 = a0_0x360877
          , _0xe1afb0 = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i[_0x38db90(0xed)](navigator[_0x38db90(0x159)]);
        return _0xe1afb0 ? 0x2 : 0x4;
    },
    'clone': function() {
        const _0x2eb6b8 = a0_0x360877;
        return new S[(_0x2eb6b8(0x13b))]({
            'x': this['x'],
            'y': this['y'],
            'z': this['z'],
            'a': this['a'],
            'h': this['h']
        });
    },
    '_draw': function() {
        const _0xce710d = a0_0x360877
          , _0x1384d6 = window[_0xce710d(0xfe)] || settings
          , _0x22d42f = hexToRgb(_0x1384d6[_0xce710d(0x13d)]);
        this['c']['r'] = _0x22d42f['r'],
        this['c']['g'] = _0x22d42f['g'],
        this['c']['b'] = _0x22d42f['b'],
        this['c']['a'] = this['p']['a'],
        S[_0xce710d(0x13f)]['drawCircle'](this['p'], this['c']);
    },
    '_moveTowards': function(_0x4ee54c) {
        const _0x115e4c = a0_0x360877;
        var _0x786422 = this[_0x115e4c(0x107)](_0x4ee54c, !![])
          , _0x3778e8 = _0x786422[0x0]
          , _0x8f55c1 = _0x786422[0x1]
          , _0x38e337 = _0x786422[0x2]
          , _0x535c95 = this['e'] * _0x38e337;
        if (this['p']['h'] === -0x1)
            return this['p']['x'] = _0x4ee54c['x'],
            this['p']['y'] = _0x4ee54c['y'],
            !![];
        if (_0x38e337 > 0x1)
            this['p']['x'] -= _0x3778e8 / _0x38e337 * _0x535c95,
            this['p']['y'] -= _0x8f55c1 / _0x38e337 * _0x535c95;
        else {
            if (this['p']['h'] > 0x0)
                this['p']['h']--;
            else
                return !![];
        }
        return ![];
    },
    '_update': function() {
        const _0x25dcd1 = a0_0x360877;
        if (this[_0x25dcd1(0x12a)](this['t'])) {
            var _0x23150c = this['q'][_0x25dcd1(0x1bd)]();
            if (_0x23150c)
                this['t']['x'] = _0x23150c['x'] || this['p']['x'],
                this['t']['y'] = _0x23150c['y'] || this['p']['y'],
                this['t']['z'] = _0x23150c['z'] || this['p']['z'],
                this['t']['a'] = _0x23150c['a'] || this['p']['a'],
                this['p']['h'] = _0x23150c['h'] || 0x0;
            else {
                if (this['s']) {
                    const _0x14dbab = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i[_0x25dcd1(0xed)](navigator[_0x25dcd1(0x159)])
                      , _0xd09e7b = _0x14dbab ? 0.1 : 3.142;
                    this['p']['x'] -= Math[_0x25dcd1(0x1ac)](Math[_0x25dcd1(0x161)]() * _0xd09e7b),
                    this['p']['y'] -= Math[_0x25dcd1(0x1ac)](Math[_0x25dcd1(0x161)]() * _0xd09e7b);
                } else
                    this[_0x25dcd1(0x170)](new S['Point']({
                        'x': this['p']['x'] + Math[_0x25dcd1(0x161)]() * 0x32 - 0x19,
                        'y': this['p']['y'] + Math[_0x25dcd1(0x161)]() * 0x32 - 0x19
                    }));
            }
        }
        d = this['p']['a'] - this['t']['a'],
        this['p']['a'] = Math['max'](0.1, this['p']['a'] - d * 0.05),
        d = this['p']['z'] - this['t']['z'],
        this['p']['z'] = Math[_0x25dcd1(0x150)](0x1, this['p']['z'] - d * 0.05);
    },
    'distanceTo': function(_0x52788d, _0x55d011) {
        const _0x5aa676 = a0_0x360877;
        var _0x49d7c2 = this['p']['x'] - _0x52788d['x']
          , _0x3658c7 = this['p']['y'] - _0x52788d['y']
          , _0x3a661e = Math[_0x5aa676(0x16c)](_0x49d7c2 * _0x49d7c2 + _0x3658c7 * _0x3658c7);
        return _0x55d011 ? [_0x49d7c2, _0x3658c7, _0x3a661e] : _0x3a661e;
    },
    'move': function(_0x28d3e3, _0x28d818) {
        const _0x89d444 = a0_0x360877;
        (!_0x28d818 || _0x28d818 && this[_0x89d444(0x107)](_0x28d3e3) > 0x1) && this['q'][_0x89d444(0x1c2)](_0x28d3e3);
    },
    'render': function() {
        const _0x4cd196 = a0_0x360877;
        this[_0x4cd196(0x199)](),
        this[_0x4cd196(0x19e)]();
    }
},
S['ShapeBuilder'] = (function() {
    const _0x1a4fd2 = a0_0x360877;
    var _0x56b8a5 = document[_0x1a4fd2(0x135)]('canvas')
      , _0x1aa2e0 = _0x56b8a5[_0x1a4fd2(0x1a1)]('2d')
      , _0x130594 = 0x1f4
      , _0x5d6324 = _0x1a4fd2(0x19a);
    function _0x4c5841() {
        const _0x238bcc = _0x1a4fd2
          , _0x46db98 = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i[_0x238bcc(0xed)](navigator[_0x238bcc(0x159)]);
        return _0x46db98 ? 0x4 : 0x8;
    }
    function _0x4051bb() {
        const _0x12ebe3 = _0x1a4fd2
          , _0x2b1d6e = _0x4c5841();
        _0x56b8a5[_0x12ebe3(0x174)] = Math[_0x12ebe3(0x124)](window['innerWidth'] / _0x2b1d6e) * _0x2b1d6e,
        _0x56b8a5[_0x12ebe3(0x15f)] = Math[_0x12ebe3(0x124)](window[_0x12ebe3(0x12e)] / _0x2b1d6e) * _0x2b1d6e,
        _0x1aa2e0[_0x12ebe3(0x184)] = _0x12ebe3(0x18a),
        _0x1aa2e0[_0x12ebe3(0x105)] = _0x12ebe3(0xef),
        _0x1aa2e0[_0x12ebe3(0x173)] = 'center';
    }
    function _0x1173bf() {
        const _0x20cf16 = _0x1a4fd2
          , _0x1f4e57 = _0x4c5841();
        var _0x571931 = _0x1aa2e0['getImageData'](0x0, 0x0, _0x56b8a5[_0x20cf16(0x174)], _0x56b8a5['height'])['data']
          , _0x156715 = []
          , _0x1c319a = 0x0
          , _0x9093c5 = 0x0
          , _0x1cc4df = _0x56b8a5[_0x20cf16(0x174)]
          , _0x1c37ef = _0x56b8a5[_0x20cf16(0x15f)]
          , _0x21db60 = 0x0
          , _0x5b9719 = 0x0;
        for (var _0x1f7980 = 0x0; _0x1f7980 < _0x571931[_0x20cf16(0x18e)]; _0x1f7980 += 0x4 * _0x1f4e57) {
            _0x571931[_0x1f7980 + 0x3] > 0x0 && (_0x156715[_0x20cf16(0x1c2)](new S[(_0x20cf16(0x13b))]({
                'x': _0x1c319a,
                'y': _0x9093c5
            })),
            _0x21db60 = _0x1c319a > _0x21db60 ? _0x1c319a : _0x21db60,
            _0x5b9719 = _0x9093c5 > _0x5b9719 ? _0x9093c5 : _0x5b9719,
            _0x1cc4df = _0x1c319a < _0x1cc4df ? _0x1c319a : _0x1cc4df,
            _0x1c37ef = _0x9093c5 < _0x1c37ef ? _0x9093c5 : _0x1c37ef),
            _0x1c319a += _0x1f4e57,
            _0x1c319a >= _0x56b8a5[_0x20cf16(0x174)] && (_0x1c319a = 0x0,
            _0x9093c5 += _0x1f4e57,
            _0x1f7980 += _0x1f4e57 * 0x4 * _0x56b8a5[_0x20cf16(0x174)]);
        }
        return {
            'dots': _0x156715,
            'w': _0x21db60 + _0x1cc4df,
            'h': _0x5b9719 + _0x1c37ef
        };
    }
    function _0x56a646(_0x9006b0) {
        const _0x22678f = _0x1a4fd2;
        _0x1aa2e0[_0x22678f(0x1ba)] = _0x22678f(0xfc) + _0x9006b0 + _0x22678f(0x10f) + _0x5d6324;
    }
    function _0x337074(_0x5310fa) {
        return !isNaN(parseFloat(_0x5310fa)) && isFinite(_0x5310fa);
    }
    function _0x342d3c() {
        const _0x41daf3 = _0x1a4fd2;
        _0x4051bb(),
        window[_0x41daf3(0x143)]('resize', _0x4051bb);
    }
    return _0x342d3c(),
    {
        'circle': function(_0x53f0e6) {
            const _0xbf6f78 = _0x1a4fd2;
            var _0x24ab76 = Math[_0xbf6f78(0x150)](0x0, _0x53f0e6) / 0x2;
            const _0x135c1a = _0x4c5841();
            return _0x1aa2e0[_0xbf6f78(0x185)](0x0, 0x0, _0x56b8a5[_0xbf6f78(0x174)], _0x56b8a5[_0xbf6f78(0x15f)]),
            _0x1aa2e0[_0xbf6f78(0x197)](),
            _0x1aa2e0['arc'](_0x24ab76 * _0x135c1a, _0x24ab76 * _0x135c1a, _0x24ab76 * _0x135c1a, 0x0, 0x2 * Math['PI'], ![]),
            _0x1aa2e0[_0xbf6f78(0x1c9)](),
            _0x1aa2e0[_0xbf6f78(0x188)](),
            _0x1173bf();
        },
        'letter': function(_0x3bf449) {
            const _0x46df53 = _0x1a4fd2;
            var _0x486c4c = 0x0;
            const _0x2fc193 = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i['test'](navigator[_0x46df53(0x159)])
              , _0x18d7e7 = window[_0x46df53(0x1ce)] < 0x300
              , _0xd67bac = _0x2fc193 || _0x18d7e7 ? 0xfa : 0x1f4;
            return _0x56a646(_0xd67bac),
            _0x486c4c = Math['min'](_0xd67bac, _0x56b8a5[_0x46df53(0x174)] / _0x1aa2e0['measureText'](_0x3bf449)['width'] * 0.8 * _0xd67bac, _0x56b8a5[_0x46df53(0x15f)] / _0xd67bac * (_0x337074(_0x3bf449) ? 0.8 : 0.35) * _0xd67bac),
            _0x56a646(_0x486c4c),
            _0x1aa2e0[_0x46df53(0x185)](0x0, 0x0, _0x56b8a5[_0x46df53(0x174)], _0x56b8a5[_0x46df53(0x15f)]),
            _0x1aa2e0[_0x46df53(0x1cd)](_0x3bf449, _0x56b8a5['width'] / 0x2, _0x56b8a5[_0x46df53(0x15f)] / 0x2),
            _0x1173bf();
        }
    };
}()),
S[a0_0x360877(0x1a3)] = (function() {
    var _0x231f6c = []
      , _0x7b3547 = 0x0
      , _0xcaac23 = 0x0
      , _0x2354c5 = 0x0
      , _0x194789 = 0x0;
    function _0x4c7a94() {
        const _0x1f91f2 = a0_0x48de;
        var _0x11223b = S[_0x1f91f2(0x13f)]['getArea']();
        _0x2354c5 = _0x11223b['w'] / 0x2 - _0x7b3547 / 0x2,
        _0x194789 = _0x11223b['h'] / 0x2 - _0xcaac23 / 0x2;
    }
    function _0x23a4b9() {
        const _0x559aa7 = a0_0x48de
          , _0x2e6877 = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i[_0x559aa7(0xed)](navigator['userAgent'])
          , _0x1c1679 = window[_0x559aa7(0x1ce)] < 0x300;
        return _0x2e6877 || _0x1c1679 ? {
            'minSize': 0x1,
            'maxSize': 0x4,
            'minZ': 0x2,
            'maxZ': 0x3
        } : {
            'minSize': 0x3,
            'maxSize': 0xc,
            'minZ': 0x4,
            'maxZ': 0x8
        };
    }
    return {
        'switchShape': function(_0x4b7e1e, _0x3f113e) {
            const _0x442f97 = a0_0x48de;
            var _0x2e5a11, _0x45a86d = S[_0x442f97(0x13f)]['getArea']();
            _0x7b3547 = _0x4b7e1e['w'],
            _0xcaac23 = _0x4b7e1e['h'],
            _0x4c7a94();
            const _0x568c2c = _0x23a4b9();
            if (_0x4b7e1e[_0x442f97(0x139)][_0x442f97(0x18e)] > _0x231f6c[_0x442f97(0x18e)]) {
                _0x2e5a11 = _0x4b7e1e[_0x442f97(0x139)]['length'] - _0x231f6c[_0x442f97(0x18e)];
                for (var _0x12f77c = 0x1; _0x12f77c <= _0x2e5a11; _0x12f77c++) {
                    _0x231f6c[_0x442f97(0x1c2)](new S['Dot'](_0x45a86d['w'] / 0x2,_0x45a86d['h'] / 0x2));
                }
            }
            var _0x12f77c = 0x0
              , _0x6d3672 = 0x0;
            while (_0x4b7e1e[_0x442f97(0x139)]['length'] > 0x0) {
                _0x6d3672 = Math[_0x442f97(0x124)](Math[_0x442f97(0x161)]() * _0x4b7e1e[_0x442f97(0x139)][_0x442f97(0x18e)]);
                const _0x5c85d7 = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i[_0x442f97(0xed)](navigator[_0x442f97(0x159)]);
                _0x231f6c[_0x12f77c]['e'] = _0x5c85d7 ? 0.35 : 0.11,
                _0x231f6c[_0x12f77c]['s'] ? _0x231f6c[_0x12f77c][_0x442f97(0x170)](new S[(_0x442f97(0x13b))]({
                    'z': Math[_0x442f97(0x161)]() * (_0x568c2c['maxSize'] - _0x568c2c['minSize']) + _0x568c2c[_0x442f97(0x117)],
                    'a': Math[_0x442f97(0x161)](),
                    'h': 0x12
                })) : _0x231f6c[_0x12f77c]['move'](new S[(_0x442f97(0x13b))]({
                    'z': Math[_0x442f97(0x161)]() * _0x568c2c[_0x442f97(0x111)] + _0x568c2c['minZ'],
                    'h': _0x3f113e ? 0x12 : 0x1e
                })),
                _0x231f6c[_0x12f77c]['s'] = !![],
                _0x231f6c[_0x12f77c][_0x442f97(0x170)](new S[(_0x442f97(0x13b))]({
                    'x': _0x4b7e1e[_0x442f97(0x139)][_0x6d3672]['x'] + _0x2354c5,
                    'y': _0x4b7e1e[_0x442f97(0x139)][_0x6d3672]['y'] + _0x194789,
                    'a': 0x1,
                    'z': _0x568c2c[_0x442f97(0x111)],
                    'h': 0x0
                })),
                _0x4b7e1e['dots'] = _0x4b7e1e['dots'][_0x442f97(0x1c0)](0x0, _0x6d3672)['concat'](_0x4b7e1e[_0x442f97(0x139)][_0x442f97(0x1c0)](_0x6d3672 + 0x1)),
                _0x12f77c++;
            }
            for (var _0x6d3672 = _0x12f77c; _0x6d3672 < _0x231f6c[_0x442f97(0x18e)]; _0x6d3672++) {
                _0x231f6c[_0x6d3672]['s'] && (_0x231f6c[_0x6d3672][_0x442f97(0x170)](new S[(_0x442f97(0x13b))]({
                    'z': Math['random']() * (_0x568c2c[_0x442f97(0x1c4)] - _0x568c2c[_0x442f97(0x117)]) + _0x568c2c['minSize'],
                    'a': Math[_0x442f97(0x161)](),
                    'h': 0x14
                })),
                _0x231f6c[_0x6d3672]['s'] = ![],
                _0x231f6c[_0x6d3672]['e'] = 0.04,
                _0x231f6c[_0x6d3672]['move'](new S[(_0x442f97(0x13b))]({
                    'x': Math['random']() * _0x45a86d['w'],
                    'y': Math['random']() * _0x45a86d['h'],
                    'a': 0.3,
                    'z': Math[_0x442f97(0x161)]() * _0x568c2c[_0x442f97(0x111)],
                    'h': 0x0
                })));
            }
        },
        'render': function() {
            const _0x53a260 = a0_0x48de;
            for (var _0xfb8d60 = 0x0; _0xfb8d60 < _0x231f6c[_0x53a260(0x18e)]; _0xfb8d60++) {
                _0x231f6c[_0xfb8d60][_0x53a260(0x1c1)]();
            }
        }
    };
}());
const heartPool = []
  , maxFloatingHearts = 0x19;
function createFloatingHeart() {
    const _0x1ac135 = a0_0x360877
      , _0x25af36 = document[_0x1ac135(0x135)](_0x1ac135(0x1ae));
    return _0x25af36[_0x1ac135(0x1b4)] = _0x1ac135(0x1b5),
    _0x25af36;
}
function getHeartFromPool() {
    const _0x3948fd = a0_0x360877;
    if (heartPool[_0x3948fd(0x18e)] > 0x0)
        return heartPool[_0x3948fd(0x14e)]();
    return createFloatingHeart();
}
function returnHeartToPool(_0x148e75) {
    const _0x324c99 = a0_0x360877;
    _0x148e75[_0x324c99(0x1be)](),
    heartPool['push'](_0x148e75);
}
function showFloatingHearts() {
    const _0x2e67f3 = ['❤️', '💕', '💖', '💗', '💓', '💞'];
    let _0x212781 = 0x0;
    function _0x7d15ae() {
        const _0x533c0e = a0_0x48de;
        if (_0x212781 >= maxFloatingHearts)
            return;
        const _0x114053 = getHeartFromPool();
        _0x114053[_0x533c0e(0x132)] = _0x2e67f3[Math['floor'](Math['random']() * _0x2e67f3[_0x533c0e(0x18e)])],
        _0x114053['style'][_0x533c0e(0x137)] = Math[_0x533c0e(0x161)]() * 0x64 + '%',
        _0x114053['style'][_0x533c0e(0x178)] = '100%',
        _0x114053[_0x533c0e(0x13c)][_0x533c0e(0x19f)] = Math[_0x533c0e(0x161)]() * 0x14 + 0xf + 'px',
        document['body'][_0x533c0e(0x114)](_0x114053),
        _0x212781++,
        setTimeout( () => returnHeartToPool(_0x114053), 0x2710),
        _0x212781 < maxFloatingHearts && setTimeout(_0x7d15ae, 0x640);
    }
    _0x7d15ae();
}
function showBook() {
    const _0x4eb7a9 = a0_0x360877
      , _0x1a830b = document[_0x4eb7a9(0x175)](_0x4eb7a9(0x193))
      , _0x136486 = document[_0x4eb7a9(0x16e)](_0x4eb7a9(0x148));
    showStars(),
    _0x1a830b && _0x136486 && (_0x136486[_0x4eb7a9(0x13c)][_0x4eb7a9(0x125)] = _0x4eb7a9(0x147),
    _0x136486['classList']['add'](_0x4eb7a9(0x16a)),
    _0x1a830b[_0x4eb7a9(0x13c)][_0x4eb7a9(0x125)] = _0x4eb7a9(0x147),
    requestAnimationFrame( () => {
        const _0x165f47 = _0x4eb7a9;
        _0x1a830b[_0x165f47(0x13c)]['opacity'] = '0',
        _0x1a830b['style'][_0x165f47(0xf0)] = _0x165f47(0x167),
        _0x1a830b[_0x165f47(0x13c)][_0x165f47(0x1b9)] = _0x165f47(0x149),
        requestAnimationFrame( () => {
            const _0x1a2540 = _0x165f47;
            _0x1a830b[_0x1a2540(0x13c)]['opacity'] = '1',
            _0x1a830b[_0x1a2540(0x13c)][_0x1a2540(0xf0)] = _0x1a2540(0x151),
            setTimeout( () => {
                !isPlaying && toggleMusic();
            }
            , 0x320);
        }
        );
    }
    ));
}
function hexToRgb(_0x583ed3) {
    const _0x382e9b = a0_0x360877
      , _0x55e6d0 = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i[_0x382e9b(0x195)](_0x583ed3);
    return _0x55e6d0 ? {
        'r': parseInt(_0x55e6d0[0x1], 0x10),
        'g': parseInt(_0x55e6d0[0x2], 0x10),
        'b': parseInt(_0x55e6d0[0x3], 0x10)
    } : {
        'r': 0xd3,
        'g': 0x9b,
        'b': 0x9b
    };
}
const book = document['getElementById'](a0_0x360877(0x193))
  , contentDisplay = document[a0_0x360877(0x175)](a0_0x360877(0x1a5))
  , contentText = document[a0_0x360877(0x175)]('contentText');
let currentPage = 0x0, isFlipping = ![], typewriterTimeout, isBookFinished = ![], photoUrls = pages[a0_0x360877(0x1c3)](_0x4a8b5e => _0x4a8b5e[a0_0x360877(0x106)])[a0_0x360877(0x11a)](_0x17c68d => _0x17c68d[a0_0x360877(0x106)]);
function showConfetti() {
    const _0x246cd9 = a0_0x360877
      , _0x24d338 = [_0x246cd9(0x15b), '#ff9671', _0x246cd9(0x112), _0x246cd9(0x187), _0x246cd9(0x104)];
    let _0x4add3d = 0x0;
    function _0x2f320a() {
        const _0x551cbe = _0x246cd9;
        if (_0x4add3d >= maxConfetti)
            return;
        const _0x1765d1 = getConfettiFromPool();
        _0x1765d1[_0x551cbe(0x13c)][_0x551cbe(0x1ad)] = _0x24d338[Math[_0x551cbe(0x124)](Math[_0x551cbe(0x161)]() * _0x24d338[_0x551cbe(0x18e)])],
        _0x1765d1[_0x551cbe(0x13c)][_0x551cbe(0x183)](_0x551cbe(0x1bc), Math[_0x551cbe(0x161)]() * 0x190 - 0xc8 + 'px'),
        _0x1765d1['style'][_0x551cbe(0x183)](_0x551cbe(0x171), Math[_0x551cbe(0x161)]() * -0x190 + 'px'),
        _0x1765d1[_0x551cbe(0x13c)][_0x551cbe(0x137)] = window[_0x551cbe(0x1ce)] / 0x2 + 'px',
        _0x1765d1[_0x551cbe(0x13c)][_0x551cbe(0x178)] = window[_0x551cbe(0x12e)] / 0x2 + 'px',
        document['body']['appendChild'](_0x1765d1),
        setTimeout( () => returnConfettiToPool(_0x1765d1), 0x3e8),
        _0x4add3d++,
        _0x4add3d < maxConfetti && setTimeout(_0x2f320a, 0x14);
    }
    _0x2f320a();
}
let fireworkContainer = null;
function showFirework() {
    const _0x12b7cc = a0_0x360877;
    !fireworkContainer && (fireworkContainer = document['getElementById']('fireworkContainer'));
    fireworkContainer[_0x12b7cc(0x132)] = '',
    fireworkContainer[_0x12b7cc(0x13c)]['opacity'] = 0x1;
    const _0x573504 = document[_0x12b7cc(0x108)]();
    for (let _0x14579c = 0x0; _0x14579c < 0x14; _0x14579c++) {
        const _0x483314 = document[_0x12b7cc(0x135)](_0x12b7cc(0x1ae));
        _0x483314[_0x12b7cc(0x1b4)] = 'firework',
        _0x483314[_0x12b7cc(0x13c)][_0x12b7cc(0xf0)] = _0x12b7cc(0x186) + _0x14579c * 0x12 + _0x12b7cc(0x1d2),
        _0x573504[_0x12b7cc(0x114)](_0x483314);
    }
    fireworkContainer[_0x12b7cc(0x114)](_0x573504),
    requestAnimationFrame( () => {
        setTimeout( () => {
            const _0x412b5a = a0_0x48de;
            fireworkContainer[_0x412b5a(0x13c)][_0x412b5a(0x10d)] = 0x0;
        }
        , 0x3e8);
    }
    );
}
const photoCache = new Map();
let heartPhotosCreated = 0x0;
const maxHeartPhotos = 0x1e;
function preloadPhoto(_0x4c1464) {
    const _0x69d60c = a0_0x360877;
    if (photoCache['has'](_0x4c1464))
        return photoCache[_0x69d60c(0x115)](_0x4c1464);
    const _0x393cba = new Image();
    return _0x393cba['src'] = _0x4c1464,
    photoCache[_0x69d60c(0x162)](_0x4c1464, _0x393cba),
    _0x393cba;
}
function createHeartPhotoCentered(_0x1a1cd6, _0x2fa186) {
    const _0x488b4b = a0_0x360877;
    if (heartPhotosCreated >= maxHeartPhotos)
        return;
    const _0xe5732b = photoUrls[_0x1a1cd6 % photoUrls[_0x488b4b(0x18e)]]
      , _0x3f78fa = preloadPhoto(_0xe5732b)
      , _0x56e8e2 = document['createElement'](_0x488b4b(0xf2));
    _0x56e8e2['src'] = _0xe5732b,
    _0x56e8e2[_0x488b4b(0x1b4)] = _0x488b4b(0x166),
    _0x56e8e2[_0x488b4b(0x13c)][_0x488b4b(0x16b)] = '300';
    const _0x214d22 = window['innerWidth'] * 0.5
      , _0xaa148a = window[_0x488b4b(0x12e)] * 0.5
      , _0x2e4ab6 = _0x1a1cd6 / _0x2fa186 * 0x2 * Math['PI']
      , _0x344174 = window[_0x488b4b(0x12e)] <= 0x1f4 && window['innerWidth'] > window['innerHeight']
      , _0x2ecc25 = _0x344174 ? 0x8 : 0x10
      , _0x5b7cb0 = Math[_0x488b4b(0x1ac)](_0x2e4ab6)
      , _0x50d4ce = Math[_0x488b4b(0x11c)](_0x2e4ab6)
      , _0x23b77d = _0x2ecc25 * 0x10 * Math[_0x488b4b(0x12d)](_0x5b7cb0, 0x3)
      , _0x24d486 = -_0x2ecc25 * (0xd * _0x50d4ce - 0x5 * Math['cos'](0x2 * _0x2e4ab6) - 0x2 * Math[_0x488b4b(0x11c)](0x3 * _0x2e4ab6) - Math[_0x488b4b(0x11c)](0x4 * _0x2e4ab6));
    _0x56e8e2['style'][_0x488b4b(0x137)] = _0x214d22 + 'px',
    _0x56e8e2[_0x488b4b(0x13c)]['top'] = _0xaa148a + 'px',
    _0x56e8e2[_0x488b4b(0x13c)][_0x488b4b(0x10d)] = '0',
    _0x56e8e2['style'][_0x488b4b(0xf0)] = _0x488b4b(0x1c7),
    _0x56e8e2['style'][_0x488b4b(0x1b9)] = _0x488b4b(0x1d0),
    document[_0x488b4b(0x1ab)][_0x488b4b(0x114)](_0x56e8e2),
    heartPhotosCreated++,
    requestAnimationFrame( () => {
        const _0x1d0210 = _0x488b4b;
        _0x56e8e2[_0x1d0210(0x13c)][_0x1d0210(0x10d)] = '1',
        _0x56e8e2['style'][_0x1d0210(0xf0)] = 'translate(-50%,\x20-50%)\x20scale(1)',
        _0x56e8e2['style']['left'] = _0x214d22 + _0x23b77d + 'px',
        _0x56e8e2['style'][_0x1d0210(0x178)] = _0xaa148a + _0x24d486 + 'px';
    }
    );
}
function spawnHeartPhotosCentered() {
    const _0x40eb14 = a0_0x360877;
    heartPhotosCreated = 0x0,
    photoUrls[_0x40eb14(0x12b)](_0x2e6627 => preloadPhoto(_0x2e6627));
    let _0x49deb4 = 0x0;
    function _0x41b743() {
        _0x49deb4 < maxHeartPhotos && (createHeartPhotoCentered(_0x49deb4, maxHeartPhotos),
        _0x49deb4++,
        setTimeout( () => {
            requestAnimationFrame(_0x41b743);
        }
        , 0x50));
    }
    _0x41b743();
}
function startHeartEffect() {
    const _0x1de62c = a0_0x360877
      , _0x22a32d = window['settings'] || settings;
    if (!_0x22a32d[_0x1de62c(0x1a7)])
        return;
    const _0x2ed773 = document['getElementById'](_0x1de62c(0x193))
      , _0x30946d = document[_0x1de62c(0x16e)](_0x1de62c(0x148))
      , _0x30e8ef = document[_0x1de62c(0x175)](_0x1de62c(0x1a5));
    _0x2ed773 && (_0x2ed773[_0x1de62c(0x13c)][_0x1de62c(0x125)] = 'none',
    _0x2ed773['classList']['remove'](_0x1de62c(0x16a))),
    _0x30946d && (_0x30946d[_0x1de62c(0x13c)][_0x1de62c(0x125)] = _0x1de62c(0x17e),
    _0x30946d['classList'][_0x1de62c(0x1be)](_0x1de62c(0x16a))),
    _0x30e8ef && _0x30e8ef[_0x1de62c(0xf4)][_0x1de62c(0x1be)](_0x1de62c(0x16a)),
    requestAnimationFrame( () => {
        setTimeout( () => {
            showConfetti();
        }
        , 0x64),
        setTimeout( () => {
            showFirework();
        }
        , 0xc8),
        setTimeout( () => {
            spawnHeartPhotosCentered();
        }
        , 0x12c);
    }
    );
}
function checkBookFinished() {
    const _0x2240f5 = a0_0x360877
      , _0x2518cc = Math['ceil'](pages[_0x2240f5(0x18e)] / 0x2)
      , _0x49a7de = _0x2518cc - 0x1
      , _0x4997ee = document[_0x2240f5(0x16e)](_0x2240f5(0x156) + _0x49a7de + '\x22]');
    if (currentPage === _0x49a7de && _0x4997ee && _0x4997ee[_0x2240f5(0xf4)][_0x2240f5(0x1b7)](_0x2240f5(0xeb))) {
        if (!isBookFinished) {
            isBookFinished = !![];
            const _0x40dcd5 = document[_0x2240f5(0x175)]('contentDisplay');
            _0x40dcd5 && _0x40dcd5[_0x2240f5(0xf4)][_0x2240f5(0x1be)](_0x2240f5(0x16a)),
            setTimeout( () => {
                const _0x421c00 = _0x2240f5
                  , _0x5837c3 = window[_0x421c00(0xfe)] || settings;
                _0x5837c3[_0x421c00(0x1a7)] && startHeartEffect();
            }
            , 0x3e8);
        }
    }
}
function nextPage() {
    const _0x45b86c = a0_0x360877
      , _0xa5b8ad = Math['ceil'](pages['length'] / 0x2);
    if (currentPage < _0xa5b8ad - 0x1 && !isFlipping) {
        isFlipping = !![];
        const _0x41cf70 = document[_0x45b86c(0x16e)]('.page[data-page=\x22' + currentPage + '\x22]');
        _0x41cf70[_0x45b86c(0xf4)][_0x45b86c(0x189)](_0x45b86c(0x192)),
        setTimeout( () => {
            const _0xa83ccd = _0x45b86c;
            _0x41cf70[_0xa83ccd(0xf4)][_0xa83ccd(0x1be)](_0xa83ccd(0x192)),
            _0x41cf70['classList'][_0xa83ccd(0x189)](_0xa83ccd(0xeb)),
            currentPage++,
            isFlipping = ![],
            showPageContent(),
            checkBookFinished();
        }
        , 0x190);
    } else {
        if (currentPage === _0xa5b8ad - 0x1 && !isFlipping) {
            const _0x1abb0f = document['querySelector'](_0x45b86c(0x156) + currentPage + '\x22]');
            _0x1abb0f && !_0x1abb0f[_0x45b86c(0xf4)][_0x45b86c(0x1b7)](_0x45b86c(0xeb)) && (isFlipping = !![],
            _0x1abb0f['classList']['add'](_0x45b86c(0x192)),
            setTimeout( () => {
                const _0x41289f = _0x45b86c;
                _0x1abb0f[_0x41289f(0xf4)]['remove']('flipping'),
                _0x1abb0f[_0x41289f(0xf4)][_0x41289f(0x189)](_0x41289f(0xeb)),
                isFlipping = ![],
                showPageContent(),
                checkBookFinished();
            }
            , 0x190));
        }
    }
}
function prevPage() {
    const _0x37188d = a0_0x360877;
    if (currentPage > 0x0 && !isFlipping) {
        isFlipping = !![],
        currentPage--;
        const _0x562ae8 = document[_0x37188d(0x16e)](_0x37188d(0x156) + currentPage + '\x22]');
        _0x562ae8[_0x37188d(0xf4)][_0x37188d(0x189)]('flipping'),
        setTimeout( () => {
            const _0x4ae01e = _0x37188d;
            _0x562ae8[_0x4ae01e(0xf4)][_0x4ae01e(0x1be)](_0x4ae01e(0x192)),
            _0x562ae8[_0x4ae01e(0xf4)][_0x4ae01e(0x1be)]('flipped'),
            isFlipping = ![],
            showPageContent(),
            isBookFinished = ![];
        }
        , 0x190);
    }
}
function typewriterEffect(_0x1d1460, _0x1d0876, _0x31d852=0x32) {
    return new Promise(_0x15b95f => {
        const _0x3f6bac = a0_0x48de;
        _0x1d1460[_0x3f6bac(0x132)] = '';
        let _0x355f21 = 0x0
          , _0x3305f4 = 0x0;
        function _0x4339bb() {
            const _0x46d368 = _0x3f6bac;
            if (_0x355f21 < _0x1d0876['length']) {
                _0x1d1460[_0x46d368(0x132)] += _0x1d0876[_0x46d368(0x1af)](_0x355f21),
                _0x355f21++;
                const _0x5d0c9b = Date[_0x46d368(0x177)]();
                if (_0x5d0c9b - _0x3305f4 > 0x64) {
                    const _0x4b34a = _0x1d1460[_0x46d368(0x1d1)](_0x46d368(0x179));
                    _0x4b34a && _0x4b34a[_0x46d368(0x123)] > _0x4b34a[_0x46d368(0x18c)] && (_0x4b34a[_0x46d368(0x1c6)] = _0x4b34a[_0x46d368(0x123)] - _0x4b34a[_0x46d368(0x18c)]),
                    _0x3305f4 = _0x5d0c9b;
                }
                _0x31d852 < 0x10 ? requestAnimationFrame(_0x4339bb) : typewriterTimeout = setTimeout(_0x4339bb, _0x31d852);
            } else
                _0x15b95f();
        }
        _0x4339bb();
    }
    );
}
async function showPageContent() {
    const _0x5a3b5f = a0_0x360877;
    typewriterTimeout && clearTimeout(typewriterTimeout);
    let _0x5b5e8a = 0x0;
    if (currentPage === 0x0)
        _0x5b5e8a = 0x0;
    else {
        const _0x15214a = document[_0x5a3b5f(0x16e)](_0x5a3b5f(0x156) + currentPage + '\x22]');
        _0x15214a && _0x15214a[_0x5a3b5f(0xf4)]['contains']('flipped') ? _0x5b5e8a = currentPage * 0x2 + 0x1 : _0x5b5e8a = currentPage * 0x2;
    }
    const _0x2648b5 = pages[_0x5b5e8a]?.[_0x5a3b5f(0x110)];
    _0x2648b5 ? (contentDisplay[_0x5a3b5f(0xf4)][_0x5a3b5f(0x189)](_0x5a3b5f(0x16a)),
    contentText[_0x5a3b5f(0x132)] = '',
    await typewriterEffect(contentText, _0x2648b5, 0x1e)) : contentDisplay[_0x5a3b5f(0xf4)][_0x5a3b5f(0x1be)]('show');
}
function createPlaceholderImage(_0x3ef6a1) {
    const _0x4c82ef = a0_0x360877;
    return _0x4c82ef(0x153) + btoa(_0x4c82ef(0x1b8) + _0x3ef6a1 + _0x4c82ef(0x127));
}
let startX = 0x0
  , startY = 0x0
  , startTime = 0x0
  , isDragging = ![]
  , currentTransform = 0x0;
function a0_0x48de(_0x12efc7, _0x42b293) {
    const _0x4c4a96 = a0_0x4c4a();
    return a0_0x48de = function(_0x48de83, _0x54d9f6) {
        _0x48de83 = _0x48de83 - 0xeb;
        let _0x549dea = _0x4c4a96[_0x48de83];
        return _0x549dea;
    }
    ,
    a0_0x48de(_0x12efc7, _0x42b293);
}
book[a0_0x360877(0x143)](a0_0x360877(0xfb), handleTouchStart, {
    'passive': ![]
}),
book['addEventListener'](a0_0x360877(0x136), handleTouchMove, {
    'passive': ![]
}),
book[a0_0x360877(0x143)](a0_0x360877(0x12f), handleTouchEnd, {
    'passive': ![]
}),
book['addEventListener'](a0_0x360877(0x120), handleMouseStart),
book['addEventListener'](a0_0x360877(0x116), handleMouseMove),
book['addEventListener'](a0_0x360877(0x11b), handleMouseEnd),
book[a0_0x360877(0x143)](a0_0x360877(0x113), handleMouseEnd);
function handleTouchStart(_0x142d69) {
    const _0x4492f7 = a0_0x360877;
    if (isFlipping)
        return;
    startX = _0x142d69[_0x4492f7(0x1a0)][0x0][_0x4492f7(0x16f)],
    startY = _0x142d69[_0x4492f7(0x1a0)][0x0][_0x4492f7(0x17a)],
    startTime = Date[_0x4492f7(0x177)](),
    isDragging = !![],
    currentTransform = 0x0;
}
function handleMouseStart(_0x5db420) {
    const _0xbb4946 = a0_0x360877;
    if (isFlipping)
        return;
    startX = _0x5db420[_0xbb4946(0x16f)],
    startY = _0x5db420['clientY'],
    startTime = Date[_0xbb4946(0x177)](),
    isDragging = !![],
    currentTransform = 0x0,
    _0x5db420[_0xbb4946(0x1b1)]();
}
function handleTouchMove(_0x905478) {
    const _0x77b748 = a0_0x360877;
    if (!isDragging || isFlipping)
        return;
    _0x905478['preventDefault']();
    const _0x1e30b2 = _0x905478[_0x77b748(0x1a0)][0x0][_0x77b748(0x16f)]
      , _0x2db9ca = _0x905478[_0x77b748(0x1a0)][0x0][_0x77b748(0x17a)]
      , _0x2bcef6 = _0x1e30b2 - startX
      , _0x28c833 = _0x2db9ca - startY;
    Math['abs'](_0x2bcef6) > Math[_0x77b748(0x145)](_0x28c833) && handleSwipeMove(_0x2bcef6);
}
function handleMouseMove(_0x35bb0f) {
    const _0x47a804 = a0_0x360877;
    if (!isDragging || isFlipping)
        return;
    const _0x5ba250 = _0x35bb0f[_0x47a804(0x16f)] - startX
      , _0x105107 = _0x35bb0f[_0x47a804(0x17a)] - startY;
    Math[_0x47a804(0x145)](_0x5ba250) > Math[_0x47a804(0x145)](_0x105107) && handleSwipeMove(_0x5ba250);
}
function handleSwipeMove(_0x3cdf59) {
    const _0x12f54c = a0_0x360877
      , _0x636cfa = 0x32
      , _0x422d4b = 0x2d;
    let _0x43c793 = Math['max'](-_0x422d4b, Math[_0x12f54c(0x158)](_0x422d4b, _0x3cdf59 / 0x3));
    currentTransform = _0x43c793;
    const _0x2836e5 = document[_0x12f54c(0x16e)](_0x12f54c(0x156) + currentPage + '\x22]');
    if (_0x2836e5 && !_0x2836e5[_0x12f54c(0xf4)][_0x12f54c(0x1b7)](_0x12f54c(0xeb)))
        _0x3cdf59 < -_0x636cfa && (_0x2836e5[_0x12f54c(0x13c)][_0x12f54c(0xf0)] = _0x12f54c(0xee) + _0x43c793 + 'deg)',
        _0x2836e5[_0x12f54c(0x13c)][_0x12f54c(0x1b3)] = _0x43c793 / 0xa + 'px\x2010px\x2020px\x20rgba(0,0,0,' + (0.3 + Math[_0x12f54c(0x145)](_0x43c793 / 0x64)) + ')');
    else {
        if (currentPage > 0x0) {
            const _0x2121fd = document['querySelector']('.page[data-page=\x22' + (currentPage - 0x1) + '\x22]');
            _0x2121fd && _0x2121fd[_0x12f54c(0xf4)][_0x12f54c(0x1b7)](_0x12f54c(0xeb)) && _0x3cdf59 > _0x636cfa && (_0x2121fd[_0x12f54c(0x13c)][_0x12f54c(0xf0)] = _0x12f54c(0xee) + (-0xb4 + Math[_0x12f54c(0x145)](_0x43c793)) + 'deg)',
            _0x2121fd[_0x12f54c(0x13c)][_0x12f54c(0x1b3)] = -_0x43c793 / 0xa + _0x12f54c(0x181) + (0.3 + Math[_0x12f54c(0x145)](_0x43c793 / 0x64)) + ')');
        }
    }
}
function handleTouchEnd(_0x170383) {
    const _0x55deaf = a0_0x360877;
    if (!isDragging || isFlipping)
        return;
    const _0x452115 = _0x170383[_0x55deaf(0x191)][0x0][_0x55deaf(0x16f)]
      , _0x4c7573 = _0x170383[_0x55deaf(0x191)][0x0][_0x55deaf(0x17a)]
      , _0x5c52e3 = _0x452115 - startX
      , _0x1b2e84 = _0x4c7573 - startY
      , _0x380702 = Date[_0x55deaf(0x177)]() - startTime;
    handleSwipeEnd(_0x5c52e3, _0x1b2e84, _0x380702);
}
function handleMouseEnd(_0x3a3361) {
    const _0x23a57c = a0_0x360877;
    if (!isDragging || isFlipping)
        return;
    const _0x1e2afb = _0x3a3361[_0x23a57c(0x16f)] - startX
      , _0x4ed75d = _0x3a3361['clientY'] - startY
      , _0x2d0c12 = Date[_0x23a57c(0x177)]() - startTime;
    handleSwipeEnd(_0x1e2afb, _0x4ed75d, _0x2d0c12);
}
function handleSwipeEnd(_0xec7c58, _0x1b63d2, _0x28e616) {
    const _0x46f4e9 = a0_0x360877;
    isDragging = ![];
    const _0x72d712 = document[_0x46f4e9(0x155)](_0x46f4e9(0xfd));
    _0x72d712['forEach'](_0x42b3b8 => {
        const _0x4e2e87 = _0x46f4e9;
        _0x42b3b8[_0x4e2e87(0x13c)][_0x4e2e87(0xf0)] = '',
        _0x42b3b8[_0x4e2e87(0x13c)][_0x4e2e87(0x1b3)] = '';
    }
    );
    const _0x4603d9 = 0x32
      , _0xec635f = Math[_0x46f4e9(0x145)](_0xec7c58) / _0x28e616;
    if (Math['abs'](_0xec7c58) > Math['abs'](_0x1b63d2) && Math[_0x46f4e9(0x145)](_0xec7c58) > _0x4603d9)
        _0xec7c58 < 0x0 ? nextPage() : prevPage();
    else
        _0xec635f > 0.5 && Math[_0x46f4e9(0x145)](_0xec7c58) > 0x1e && (_0xec7c58 < 0x0 ? nextPage() : prevPage());
}
document[a0_0x360877(0x143)](a0_0x360877(0x10b), _0x12faa1 => {
    const _0x1b554b = a0_0x360877;
    if (_0x12faa1[_0x1b554b(0x169)] === _0x1b554b(0x118) || _0x12faa1[_0x1b554b(0x169)] === '\x20')
        _0x12faa1[_0x1b554b(0x1b1)](),
        nextPage();
    else
        _0x12faa1[_0x1b554b(0x169)] === _0x1b554b(0x180) && (_0x12faa1[_0x1b554b(0x1b1)](),
        prevPage());
}
),
book[a0_0x360877(0x143)](a0_0x360877(0x121), _0x1225dd => {
    _0x1225dd['preventDefault']();
}
);
const musicControl = document[a0_0x360877(0x175)](a0_0x360877(0x163))
  , birthdayAudio = document[a0_0x360877(0x175)](a0_0x360877(0x119));
let isPlaying = ![];
birthdayAudio[a0_0x360877(0x130)] = 0.6;
function toggleMusic() {
    const _0x3d3b72 = a0_0x360877;
    isPlaying ? (birthdayAudio[_0x3d3b72(0x15e)](),
    musicControl[_0x3d3b72(0x132)] = '▶',
    musicControl['classList']['remove'](_0x3d3b72(0x1a9)),
    musicControl[_0x3d3b72(0x1d3)] = 'Play\x20Music',
    isPlaying = ![]) : birthdayAudio[_0x3d3b72(0x1a6)]()[_0x3d3b72(0xf8)]( () => {
        const _0x341a68 = _0x3d3b72;
        musicControl[_0x341a68(0x132)] = '⏸',
        musicControl[_0x341a68(0xf4)][_0x341a68(0x189)]('playing'),
        musicControl['title'] = _0x341a68(0x12c),
        isPlaying = !![];
    }
    )[_0x3d3b72(0x1aa)](_0x164c9c => {}
    );
}
musicControl[a0_0x360877(0x143)](a0_0x360877(0x172), toggleMusic),
birthdayAudio[a0_0x360877(0x143)](a0_0x360877(0x1b2), () => {}
),
birthdayAudio[a0_0x360877(0x143)]('error', _0x3e5d97 => {
    const _0xa61dce = a0_0x360877;
    musicControl['style'][_0xa61dce(0x125)] = _0xa61dce(0x17e);
}
),
document[a0_0x360877(0x143)](a0_0x360877(0x18d), () => {
    const _0x2eab2b = a0_0x360877;
    document[_0x2eab2b(0x17d)] && isPlaying && birthdayAudio[_0x2eab2b(0x15e)]();
}
);
let starsCreated = ![];
function createStars() {
    const _0x145cab = a0_0x360877;
    if (starsCreated)
        return;
    const _0x3a9373 = document[_0x145cab(0x175)](_0x145cab(0x18f));
    _0x3a9373[_0x145cab(0x132)] = '';
    const _0x3bfb59 = 0x64
      , _0x591c10 = [_0x145cab(0x1d4), _0x145cab(0x18b), 'large']
      , _0x226e32 = document[_0x145cab(0x108)]();
    for (let _0x2c79ad = 0x0; _0x2c79ad < _0x3bfb59; _0x2c79ad++) {
        const _0x496ef2 = document[_0x145cab(0x135)]('div');
        _0x496ef2['className'] = 'star\x20' + _0x591c10[Math['floor'](Math[_0x145cab(0x161)]() * _0x591c10[_0x145cab(0x18e)])],
        _0x496ef2[_0x145cab(0x13c)][_0x145cab(0x14f)] = _0x145cab(0x11d) + Math[_0x145cab(0x161)]() * 0x64 + _0x145cab(0x1c5) + Math[_0x145cab(0x161)]() * 0x64 + '%;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20animation-duration:\x20' + (Math[_0x145cab(0x161)]() * 0x3 + 0x1) + _0x145cab(0x16d) + Math[_0x145cab(0x161)]() * 0x2 + _0x145cab(0x157),
        _0x226e32[_0x145cab(0x114)](_0x496ef2);
    }
    _0x3a9373['appendChild'](_0x226e32),
    starsCreated = !![];
}
function showStars() {
    const _0x27b440 = a0_0x360877
      , _0x2777a8 = document[_0x27b440(0x175)](_0x27b440(0x18f));
    createStars(),
    _0x2777a8[_0x27b440(0x13c)]['display'] = _0x27b440(0x147);
}
function hideStars() {
    const _0x425266 = a0_0x360877
      , _0x2522fc = document[_0x425266(0x175)](_0x425266(0x18f));
    _0x2522fc[_0x425266(0x13c)][_0x425266(0x125)] = _0x425266(0x17e);
}
function cleanup() {
    const _0x4e6631 = a0_0x360877;
    typewriterTimeout && clearTimeout(typewriterTimeout),
    confettiPool['length'] = 0x0,
    heartPool['length'] = 0x0,
    photoCache[_0x4e6631(0x168)](),
    heartPhotosCreated = 0x0,
    starsCreated = ![];
}
let resizeTimeout;
function handleResize() {
    clearTimeout(resizeTimeout),
    resizeTimeout = setTimeout( () => {
        const _0x40f3bd = a0_0x48de
          , _0x2ba232 = document[_0x40f3bd(0x175)]('matrix-rain');
        _0x2ba232 && (_0x2ba232[_0x40f3bd(0x174)] = window[_0x40f3bd(0x1ce)],
        _0x2ba232[_0x40f3bd(0x15f)] = window[_0x40f3bd(0x12e)]);
    }
    , 0x64);
}
window[a0_0x360877(0x143)](a0_0x360877(0x154), handleResize),
window[a0_0x360877(0x143)]('beforeunload', cleanup);
