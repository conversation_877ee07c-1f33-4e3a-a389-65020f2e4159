// Main Birthday Website Logic
class BirthdayWebsite {
    constructor() {
        this.settings = {
            backgroundMusic: './music/happybirtday_uia.mp3',
            countdownTime: 5,
            matrixText: 'HAPPYBIRTHDAY',
            matrixColor1: '#ffb6c1',
            matrixColor2: '#ffc0cb',
            sequenceText: 'HAPPY|BIRTHDAY|MY|CUTEE|LITTLE|SWARALI|❤',
            sequenceColor: '#d39b9b',
            giftImage: '',
            enableBook: true,
            enableHeart: true,
            pages: []
        };
        this.currentSequenceIndex = 0;
        this.isPlaying = false;
        this.matrixCanvas = null;
        this.matrixCtx = null;
        this.animationId = null;
        this.heartInterval = null;
        this.currentPage = 0;
        this.totalPages = 0;
        this.isBookVisible = false;
        
        this.init();
    }

    init() {
        this.loadSettings();
        this.setupEventListeners();
        this.setupMatrixRain();
        this.setupStars();
        this.checkOrientation();
        this.startCountdown();
    }

    loadSettings() {
        // Try to load from URL parameter first
        const websiteId = birthdayAPI.getWebsiteIdFromURL();
        if (websiteId) {
            this.loadFromAPI(websiteId);
        } else {
            // Load from localStorage or use defaults
            const saved = localStorage.getItem('birthdaySettings');
            if (saved) {
                this.settings = { ...this.settings, ...JSON.parse(saved) };
            }
            this.applySettings();
        }
    }

    async loadFromAPI(websiteId) {
        try {
            const response = await birthdayAPI.getBirthdayWebsiteByWebsiteId(websiteId);
            if (response.success && response.data.settings) {
                this.settings = { ...this.settings, ...response.data.settings };
                this.applySettings();
            }
        } catch (error) {
            console.error('Failed to load website settings:', error);
            this.applySettings(); // Use default settings
        }
    }

    applySettings() {
        // Apply background music
        const audio = document.getElementById('birthdayAudio');
        if (audio && this.settings.backgroundMusic) {
            audio.src = this.settings.backgroundMusic;
        }

        // Apply other settings
        this.updateUI();
    }

    updateUI() {
        // Update settings modal with current values
        const elements = {
            'backgroundMusic': this.settings.backgroundMusic,
            'countdownTime': this.settings.countdownTime,
            'matrixText': this.settings.matrixText,
            'matrixColor1': this.settings.matrixColor1,
            'matrixColor2': this.settings.matrixColor2,
            'sequenceText': this.settings.sequenceText,
            'sequenceColor': this.settings.sequenceColor,
            'giftImage': this.settings.giftImage,
            'enableBook': this.settings.enableBook,
            'enableHeart': this.settings.enableHeart
        };

        Object.keys(elements).forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = elements[id];
                } else {
                    element.value = elements[id];
                }
            }
        });
    }

    setupEventListeners() {
        // Music control
        const musicControl = document.getElementById('musicControl');
        const audio = document.getElementById('birthdayAudio');
        
        if (musicControl && audio) {
            musicControl.addEventListener('click', () => {
                if (this.isPlaying) {
                    audio.pause();
                    musicControl.textContent = '▶';
                    musicControl.classList.remove('playing');
                } else {
                    audio.play().catch(e => console.log('Audio play failed:', e));
                    musicControl.textContent = '⏸';
                    musicControl.classList.add('playing');
                }
                this.isPlaying = !this.isPlaying;
            });
        }

        // Settings modal
        const settingsBtn = document.getElementById('settingsButton');
        const modal = document.getElementById('settingsModal');
        const closeBtn = modal?.querySelector('.close');
        const applyBtn = document.getElementById('applySettings');

        if (settingsBtn && modal) {
            settingsBtn.addEventListener('click', () => {
                modal.style.display = 'block';
                this.populateSettingsModal();
            });
        }

        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                modal.style.display = 'none';
            });
        }

        if (applyBtn) {
            applyBtn.addEventListener('click', () => {
                this.saveSettings();
                modal.style.display = 'none';
                this.applySettings();
                this.restart();
            });
        }

        // Click outside modal to close
        window.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });

        // Fullscreen button
        const fullscreenBtn = document.getElementById('fullscreenBtn');
        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', this.toggleFullscreen.bind(this));
        }

        // Window resize for orientation
        window.addEventListener('resize', () => {
            this.checkOrientation();
            this.resizeMatrix();
        });

        // Book page flipping
        this.setupBookEvents();
    }

    populateSettingsModal() {
        // Populate GIF options
        const giftSelect = document.getElementById('giftImage');
        if (giftSelect) {
            giftSelect.innerHTML = '<option value="" data-i18n="noGif">Không có</option>';
            
            // Add available GIFs
            const gifs = ['./gif/Cat Love GIF by KIKI.gif', './image/happy.gif'];
            gifs.forEach(gif => {
                const option = document.createElement('option');
                option.value = gif;
                option.textContent = gif.split('/').pop();
                giftSelect.appendChild(option);
            });
        }

        // Update all form values
        this.updateUI();
        
        // Setup book pages if enabled
        if (this.settings.enableBook) {
            this.setupBookSettings();
        }
    }

    setupBookSettings() {
        const pageConfigs = document.getElementById('pageConfigs');
        if (!pageConfigs) return;

        pageConfigs.innerHTML = '';
        
        // Add existing pages
        this.settings.pages.forEach((page, index) => {
            this.addPageConfig(index, page);
        });
        
        // Add "Add New Page" button
        const addButton = document.createElement('button');
        addButton.type = 'button';
        addButton.textContent = '➕ Thêm Trang Mới';
        addButton.onclick = () => this.addNewPage();
        pageConfigs.appendChild(addButton);
    }

    addPageConfig(index, page = {}) {
        const pageConfigs = document.getElementById('pageConfigs');
        const pageDiv = document.createElement('div');
        pageDiv.className = 'page-config';
        pageDiv.innerHTML = `
            <button type="button" class="page-config-close" onclick="this.parentElement.remove()">×</button>
            <h3>Trang ${index + 1}${index === 0 ? ' (Bìa)' : ''}</h3>
            <div class="form-group">
                <label>Hình ảnh:</label>
                <input type="file" id="pageImage${index}" accept="image/*" onchange="window.birthdayWebsite.previewImage(this, ${index})">
                <div id="imagePreview${index}" style="margin-top: 10px;">
                    ${page.image ? `<img src="${page.image}" style="max-width: 200px; max-height: 150px; border-radius: 8px;">` : ''}
                </div>
            </div>
            <div class="form-group">
                <label>Nội dung:</label>
                <textarea id="pageContent${index}" placeholder="Nhập nội dung cho trang ${index + 1}">${page.content || ''}</textarea>
            </div>
        `;
        pageConfigs.appendChild(pageDiv);
    }

    addNewPage() {
        const newIndex = this.settings.pages.length;
        this.settings.pages.push({ content: '', image: '' });
        this.addPageConfig(newIndex);
    }

    previewImage(input, index) {
        const preview = document.getElementById(`imagePreview${index}`);
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            reader.onload = (e) => {
                preview.innerHTML = `<img src="${e.target.result}" style="max-width: 200px; max-height: 150px; border-radius: 8px;">`;
                // Store the image data
                if (!this.settings.pages[index]) {
                    this.settings.pages[index] = {};
                }
                this.settings.pages[index].image = e.target.result;
            };
            reader.readAsDataURL(input.files[0]);
        }
    }

    saveSettings() {
        // Get values from form
        const formData = {
            backgroundMusic: document.getElementById('backgroundMusic')?.value || this.settings.backgroundMusic,
            countdownTime: parseInt(document.getElementById('countdownTime')?.value) || this.settings.countdownTime,
            matrixText: document.getElementById('matrixText')?.value || this.settings.matrixText,
            matrixColor1: document.getElementById('matrixColor1')?.value || this.settings.matrixColor1,
            matrixColor2: document.getElementById('matrixColor2')?.value || this.settings.matrixColor2,
            sequenceText: document.getElementById('sequenceText')?.value || this.settings.sequenceText,
            sequenceColor: document.getElementById('sequenceColor')?.value || this.settings.sequenceColor,
            giftImage: document.getElementById('giftImage')?.value || this.settings.giftImage,
            enableBook: document.getElementById('enableBook')?.value === 'true',
            enableHeart: document.getElementById('enableHeart')?.value === 'true'
        };

        // Save page data
        const pages = [];
        const pageConfigs = document.querySelectorAll('.page-config');
        pageConfigs.forEach((config, index) => {
            const contentTextarea = config.querySelector(`#pageContent${index}`);
            const imagePreview = config.querySelector(`#imagePreview${index} img`);
            
            if (contentTextarea || imagePreview) {
                pages.push({
                    content: contentTextarea?.value || '',
                    image: imagePreview?.src || this.settings.pages[index]?.image || ''
                });
            }
        });

        formData.pages = pages;
        this.settings = { ...this.settings, ...formData };
        
        // Save to localStorage
        localStorage.setItem('birthdaySettings', JSON.stringify(this.settings));
    }

    restart() {
        // Stop current animations
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        if (this.heartInterval) {
            clearInterval(this.heartInterval);
        }

        // Reset display
        document.getElementById('matrix-rain').style.display = 'none';
        document.getElementById('contentDisplay').classList.remove('show');
        document.getElementById('gift-image').style.display = 'none';
        document.querySelector('.book-container').classList.remove('show');

        // Remove existing hearts
        document.querySelectorAll('.heart').forEach(heart => heart.remove());

        // Restart sequence
        setTimeout(() => {
            this.startCountdown();
        }, 1000);
    }

    setupBookEvents() {
        const book = document.getElementById('book');
        if (book) {
            book.addEventListener('click', (e) => {
                const page = e.target.closest('.page');
                if (page && !page.classList.contains('flipping')) {
                    this.flipPage(page);
                }
            });
        }
    }

    flipPage(pageElement) {
        const pageIndex = Array.from(pageElement.parentNode.children).indexOf(pageElement);

        pageElement.classList.add('flipping');

        setTimeout(() => {
            pageElement.classList.remove('flipping');
            pageElement.classList.add('flipped');
        }, 400);
    }

    startCountdown() {
        const countdownTime = this.settings.countdownTime * 1000;

        setTimeout(() => {
            this.startSequence();
        }, countdownTime);
    }

    startSequence() {
        this.showMatrixRain();

        setTimeout(() => {
            this.showSequenceText();
        }, 2000);

        setTimeout(() => {
            this.showGiftImage();
        }, 5000);

        setTimeout(() => {
            this.showBook();
        }, 8000);

        if (this.settings.enableHeart) {
            this.startHeartEffect();
        }
    }

    showMatrixRain() {
        const canvas = document.getElementById('matrix-rain');
        canvas.style.display = 'block';
        this.startMatrixAnimation();
    }

    setupMatrixRain() {
        this.matrixCanvas = document.getElementById('matrix-rain');
        if (this.matrixCanvas) {
            this.matrixCtx = this.matrixCanvas.getContext('2d');
            this.resizeMatrix();
        }
    }

    resizeMatrix() {
        if (!this.matrixCanvas) return;

        this.matrixCanvas.width = window.innerWidth;
        this.matrixCanvas.height = window.innerHeight;
    }

    startMatrixAnimation() {
        const canvas = this.matrixCanvas;
        const ctx = this.matrixCtx;

        if (!canvas || !ctx) return;

        const columns = Math.floor(canvas.width / 20);
        const drops = Array(columns).fill(1);
        const chars = this.settings.matrixText.split('');

        const animate = () => {
            ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            ctx.font = '15px monospace';

            for (let i = 0; i < drops.length; i++) {
                const char = chars[Math.floor(Math.random() * chars.length)];
                ctx.fillStyle = Math.random() > 0.5 ? this.settings.matrixColor1 : this.settings.matrixColor2;
                ctx.fillText(char, i * 20, drops[i] * 20);

                if (drops[i] * 20 > canvas.height && Math.random() > 0.975) {
                    drops[i] = 0;
                }
                drops[i]++;
            }

            this.animationId = requestAnimationFrame(animate);
        };

        animate();
    }

    showSequenceText() {
        const contentDisplay = document.getElementById('contentDisplay');
        const contentText = document.getElementById('contentText');

        if (contentDisplay && contentText) {
            const sequences = this.settings.sequenceText.split('|');
            contentDisplay.classList.add('show');
            contentText.style.color = this.settings.sequenceColor;

            this.typewriterEffect(contentText, sequences.join(' '), () => {
                // Animation complete
            });
        }
    }

    typewriterEffect(element, text, callback) {
        element.innerHTML = '';
        let i = 0;

        const timer = setInterval(() => {
            if (i < text.length) {
                element.innerHTML += text.charAt(i);
                i++;
            } else {
                clearInterval(timer);
                if (callback) callback();
            }
        }, 100);
    }

    showGiftImage() {
        if (this.settings.giftImage) {
            const giftImg = document.getElementById('gift-image');
            if (giftImg) {
                giftImg.src = this.settings.giftImage;
                giftImg.style.display = 'block';
                giftImg.style.animation = 'giftCelebration 2s ease-in-out';
            }
        }
    }

    showBook() {
        if (this.settings.enableBook && this.settings.pages && this.settings.pages.length > 0) {
            this.createBookPages();
            const bookContainer = document.querySelector('.book-container');
            const book = document.getElementById('book');

            if (bookContainer && book) {
                bookContainer.classList.add('show');
                book.classList.add('show');
                this.isBookVisible = true;
            }
        }
    }

    createBookPages() {
        const book = document.getElementById('book');
        if (!book) return;

        book.innerHTML = '';

        this.settings.pages.forEach((page, index) => {
            const pageElement = document.createElement('div');
            pageElement.className = 'page';
            pageElement.style.zIndex = this.settings.pages.length - index;

            const frontSide = document.createElement('div');
            frontSide.className = 'page-front';

            const backSide = document.createElement('div');
            backSide.className = 'page-back';

            if (page.image) {
                const img = document.createElement('img');
                img.src = page.image;
                img.alt = page.content || `Page ${index + 1}`;
                frontSide.appendChild(img);
            } else if (page.content) {
                frontSide.innerHTML = `<div style="padding: 20px; font-size: 16px; text-align: center; color: #333;">${page.content}</div>`;
            }

            pageElement.appendChild(frontSide);
            pageElement.appendChild(backSide);
            book.appendChild(pageElement);
        });

        this.totalPages = this.settings.pages.length;
    }

    startHeartEffect() {
        this.heartInterval = setInterval(() => {
            this.createHeart();
        }, 1000);
    }

    createHeart() {
        const heart = document.createElement('div');
        heart.className = 'heart';
        heart.innerHTML = '❤️';
        heart.style.left = Math.random() * window.innerWidth + 'px';
        heart.style.top = window.innerHeight + 'px';

        document.body.appendChild(heart);

        setTimeout(() => {
            if (heart.parentNode) {
                heart.remove();
            }
        }, 4000);
    }

    setupStars() {
        const starsContainer = document.getElementById('starsContainer');
        if (!starsContainer) return;

        const numStars = 100;

        for (let i = 0; i < numStars; i++) {
            const star = document.createElement('div');
            star.className = 'star';

            // Random size
            const size = Math.random();
            if (size > 0.8) star.classList.add('large');
            else if (size > 0.5) star.classList.add('medium');
            else star.classList.add('small');

            // Random position
            star.style.left = Math.random() * 100 + '%';
            star.style.top = Math.random() * 100 + '%';

            // Random animation delay
            star.style.animationDelay = Math.random() * 2 + 's';

            starsContainer.appendChild(star);
        }

        starsContainer.style.display = 'block';
    }

    checkOrientation() {
        const orientationLock = document.getElementById('orientation-lock');
        if (!orientationLock) return;

        const isMobile = window.innerWidth <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

        if (isMobile && window.innerHeight < window.innerWidth) {
            // Landscape on mobile
            orientationLock.style.display = 'flex';
        } else {
            orientationLock.style.display = 'none';
        }
    }

    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.log('Fullscreen not supported:', err);
                alert(t('fullscreenNotSupported'));
            });
        } else {
            document.exitFullscreen();
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.birthdayWebsite = new BirthdayWebsite();
});
