// Main Birthday Website Logic
class BirthdayWebsite {
    constructor() {
        this.settings = {
            backgroundMusic: './music/happybirtday_uia.mp3',
            countdownTime: 5,
            matrixText: 'HAPPYBIRTHDAY',
            matrixColor1: '#ffb6c1',
            matrixColor2: '#ffc0cb',
            sequenceText: 'HAPPY|BIRTHDAY|MY|CUTEE|LITTLE|SWARALI|❤',
            sequenceColor: '#d39b9b',
            giftImage: '',
            enableBook: true,
            enableHeart: true,
            pages: []
        };
        this.currentSequenceIndex = 0;
        this.isPlaying = false;
        this.matrixCanvas = null;
        this.matrixCtx = null;
        this.animationId = null;
        this.heartInterval = null;
        this.currentPage = 0;
        this.totalPages = 0;
        this.isBookVisible = false;
        
        this.init();
    }

    init() {
        this.loadSettings();
        this.setupEventListeners();
        this.setupMatrixRain();
        this.setupStars();
        this.checkOrientation();
        this.startCountdown();
    }

    loadSettings() {
        // Load the specific website data
        this.loadSpecificWebsiteData();
    }

    async loadSpecificWebsiteData() {
        // Load the specific website data you provided
        const websiteData = {
            "success": true,
            "data": {
                "settings": {
                    "music": "./music/happybirtday_uia.mp3", // Using local file instead
                    "countdown": 3,
                    "matrixText": "HAPPYBIRTHDAY",
                    "matrixColor1": "#f0d1d6",
                    "matrixColor2": "#dbadb5",
                    "sequence": "HAPPY|BIRTHDAY|TO|YOU",
                    "sequenceColor": "#df96af",
                    "gift": "./gif/Cat Love GIF by KIKI.gif", // Using local file instead
                    "enableBook": true,
                    "enableHeart": false,
                    "pages": [
                        {
                            "image": "https://cdn.deargift.online/uploads/birthday-20250709-101214-az2j85.png",
                            "content": "",
                            "_id": "686e408eec531b892e97d6ad"
                        },
                        {
                            "image": "https://cdn.deargift.online/uploads/birthday-20250709-101215-ikep15.png",
                            "content": "",
                            "_id": "686e408eec531b892e97d6ae"
                        },
                        {
                            "image": "https://cdn.deargift.online/uploads/birthday-20250709-101217-5pjyn3.png",
                            "content": "Happy birthday! Chúc cậu tuổi mới luôn vui vẻ, miệng luôn mỉm cười, lòng luôn an nhiên, đời luôn hạnh phúc.",
                            "_id": "686e408eec531b892e97d6af"
                        },
                        {
                            "image": "https://cdn.deargift.online/uploads/birthday-20250709-101218-gx4tcr.png",
                            "content": "",
                            "_id": "686e408eec531b892e97d6b0"
                        },
                        {
                            "image": "https://cdn.deargift.online/uploads/birthday-20250709-101220-k4x27i.png",
                            "content": "",
                            "_id": "686e408eec531b892e97d6b1"
                        },
                        {
                            "image": "https://cdn.deargift.online/uploads/birthday-20250709-101222-xblg3r.png",
                            "content": "",
                            "_id": "686e408eec531b892e97d6b2"
                        },
                        {
                            "image": "https://cdn.deargift.online/uploads/birthday-20250709-101224-toyoh0.png",
                            "content": "",
                            "_id": "686e408eec531b892e97d6b3"
                        },
                        {
                            "image": "https://cdn.deargift.online/uploads/birthday-20250709-101226-x3bbqs.png",
                            "content": "",
                            "_id": "686e408eec531b892e97d6b4"
                        },
                        {
                            "image": "https://cdn.deargift.online/uploads/birthday-20250709-101228-8fjzx2.png",
                            "content": "",
                            "_id": "686e408eec531b892e97d6b5"
                        }
                    ]
                },
                "_id": "686e408eec531b892e97d6ac",
                "websiteId": "686e408eec531b892e97d6b6",
                "status": "Free",
                "createdAt": "2025-07-09T10:12:30.466Z",
                "__v": 0
            }
        };

        if (websiteData.success && websiteData.data.settings) {
            // Map the API field names to our internal field names
            const apiSettings = websiteData.data.settings;
            this.settings = {
                backgroundMusic: apiSettings.music || this.settings.backgroundMusic,
                countdownTime: apiSettings.countdown || this.settings.countdownTime,
                matrixText: apiSettings.matrixText || this.settings.matrixText,
                matrixColor1: apiSettings.matrixColor1 || this.settings.matrixColor1,
                matrixColor2: apiSettings.matrixColor2 || this.settings.matrixColor2,
                sequenceText: apiSettings.sequence || this.settings.sequenceText,
                sequenceColor: apiSettings.sequenceColor || this.settings.sequenceColor,
                giftImage: apiSettings.gift || this.settings.giftImage,
                enableBook: apiSettings.enableBook !== undefined ? apiSettings.enableBook : this.settings.enableBook,
                enableHeart: apiSettings.enableHeart !== undefined ? apiSettings.enableHeart : this.settings.enableHeart,
                pages: apiSettings.pages || this.settings.pages
            };

            console.log('✅ Loaded specific website data successfully!');
            console.log('📊 Settings:', this.settings);
            console.log('📖 Total pages in book:', this.settings.pages.length);
            this.applySettings();
        } else {
            console.log('Using default settings');
            this.applySettings();
        }
    }

    async loadFromAPI(websiteId) {
        try {
            const response = await birthdayAPI.getBirthdayWebsiteByWebsiteId(websiteId);
            if (response.success && response.data.settings) {
                this.settings = { ...this.settings, ...response.data.settings };
                this.applySettings();
            }
        } catch (error) {
            console.error('Failed to load website settings:', error);
            this.loadSpecificWebsiteData(); // Fallback to specific data
        }
    }

    applySettings() {
        // Apply background music
        const audio = document.getElementById('birthdayAudio');
        if (audio && this.settings.backgroundMusic) {
            audio.src = this.settings.backgroundMusic;
        }

        // Apply other settings
        this.updateUI();
    }

    updateUI() {
        // Update settings modal with current values
        const elements = {
            'backgroundMusic': this.settings.backgroundMusic,
            'countdownTime': this.settings.countdownTime,
            'matrixText': this.settings.matrixText,
            'matrixColor1': this.settings.matrixColor1,
            'matrixColor2': this.settings.matrixColor2,
            'sequenceText': this.settings.sequenceText,
            'sequenceColor': this.settings.sequenceColor,
            'giftImage': this.settings.giftImage,
            'enableBook': this.settings.enableBook,
            'enableHeart': this.settings.enableHeart
        };

        Object.keys(elements).forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = elements[id];
                } else {
                    element.value = elements[id];
                }
            }
        });
    }

    setupEventListeners() {
        // Music control
        const musicControl = document.getElementById('musicControl');
        const audio = document.getElementById('birthdayAudio');
        
        if (musicControl && audio) {
            musicControl.addEventListener('click', () => {
                if (this.isPlaying) {
                    audio.pause();
                    musicControl.textContent = '▶';
                    musicControl.classList.remove('playing');
                } else {
                    audio.play().catch(e => console.log('Audio play failed:', e));
                    musicControl.textContent = '⏸';
                    musicControl.classList.add('playing');
                }
                this.isPlaying = !this.isPlaying;
            });
        }

        // Settings modal
        const settingsBtn = document.getElementById('settingsButton');
        const modal = document.getElementById('settingsModal');
        const closeBtn = modal?.querySelector('.close');
        const applyBtn = document.getElementById('applySettings');

        if (settingsBtn && modal) {
            settingsBtn.addEventListener('click', () => {
                modal.style.display = 'block';
                this.populateSettingsModal();
            });
        }

        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                modal.style.display = 'none';
            });
        }

        if (applyBtn) {
            applyBtn.addEventListener('click', () => {
                this.saveSettings();
                modal.style.display = 'none';
                this.applySettings();
                this.restart();
            });
        }

        // Click outside modal to close
        window.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });

        // Fullscreen button
        const fullscreenBtn = document.getElementById('fullscreenBtn');
        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', this.toggleFullscreen.bind(this));
        }

        // Window resize for orientation
        window.addEventListener('resize', () => {
            this.checkOrientation();
            this.resizeMatrix();
        });

        // Book page flipping
        this.setupBookEvents();
    }

    populateSettingsModal() {
        // Populate GIF options
        const giftSelect = document.getElementById('giftImage');
        if (giftSelect) {
            giftSelect.innerHTML = '<option value="" data-i18n="noGif">Không có</option>';
            
            // Add available GIFs
            const gifs = ['./gif/Cat Love GIF by KIKI.gif', './image/happy.gif'];
            gifs.forEach(gif => {
                const option = document.createElement('option');
                option.value = gif;
                option.textContent = gif.split('/').pop();
                giftSelect.appendChild(option);
            });
        }

        // Update all form values
        this.updateUI();
        
        // Setup book pages if enabled
        if (this.settings.enableBook) {
            this.setupBookSettings();
        }
    }

    setupBookSettings() {
        const pageConfigs = document.getElementById('pageConfigs');
        if (!pageConfigs) return;

        pageConfigs.innerHTML = '';
        
        // Add existing pages
        this.settings.pages.forEach((page, index) => {
            this.addPageConfig(index, page);
        });
        
        // Add "Add New Page" button
        const addButton = document.createElement('button');
        addButton.type = 'button';
        addButton.textContent = '➕ Thêm Trang Mới';
        addButton.onclick = () => this.addNewPage();
        pageConfigs.appendChild(addButton);
    }

    addPageConfig(index, page = {}) {
        const pageConfigs = document.getElementById('pageConfigs');
        const pageDiv = document.createElement('div');
        pageDiv.className = 'page-config';
        pageDiv.innerHTML = `
            <button type="button" class="page-config-close" onclick="this.parentElement.remove()">×</button>
            <h3>Trang ${index + 1}${index === 0 ? ' (Bìa)' : ''}</h3>
            <div class="form-group">
                <label>Hình ảnh:</label>
                <input type="file" id="pageImage${index}" accept="image/*" onchange="window.birthdayWebsite.previewImage(this, ${index})">
                <div id="imagePreview${index}" style="margin-top: 10px;">
                    ${page.image ? `<img src="${page.image}" style="max-width: 200px; max-height: 150px; border-radius: 8px;">` : ''}
                </div>
            </div>
            <div class="form-group">
                <label>Nội dung:</label>
                <textarea id="pageContent${index}" placeholder="Nhập nội dung cho trang ${index + 1}">${page.content || ''}</textarea>
            </div>
        `;
        pageConfigs.appendChild(pageDiv);
    }

    addNewPage() {
        const newIndex = this.settings.pages.length;
        this.settings.pages.push({ content: '', image: '' });
        this.addPageConfig(newIndex);
    }

    previewImage(input, index) {
        const preview = document.getElementById(`imagePreview${index}`);
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            reader.onload = (e) => {
                preview.innerHTML = `<img src="${e.target.result}" style="max-width: 200px; max-height: 150px; border-radius: 8px;">`;
                // Store the image data
                if (!this.settings.pages[index]) {
                    this.settings.pages[index] = {};
                }
                this.settings.pages[index].image = e.target.result;
            };
            reader.readAsDataURL(input.files[0]);
        }
    }

    saveSettings() {
        // Get values from form
        const formData = {
            backgroundMusic: document.getElementById('backgroundMusic')?.value || this.settings.backgroundMusic,
            countdownTime: parseInt(document.getElementById('countdownTime')?.value) || this.settings.countdownTime,
            matrixText: document.getElementById('matrixText')?.value || this.settings.matrixText,
            matrixColor1: document.getElementById('matrixColor1')?.value || this.settings.matrixColor1,
            matrixColor2: document.getElementById('matrixColor2')?.value || this.settings.matrixColor2,
            sequenceText: document.getElementById('sequenceText')?.value || this.settings.sequenceText,
            sequenceColor: document.getElementById('sequenceColor')?.value || this.settings.sequenceColor,
            giftImage: document.getElementById('giftImage')?.value || this.settings.giftImage,
            enableBook: document.getElementById('enableBook')?.value === 'true',
            enableHeart: document.getElementById('enableHeart')?.value === 'true'
        };

        // Save page data
        const pages = [];
        const pageConfigs = document.querySelectorAll('.page-config');
        pageConfigs.forEach((config, index) => {
            const contentTextarea = config.querySelector(`#pageContent${index}`);
            const imagePreview = config.querySelector(`#imagePreview${index} img`);
            
            if (contentTextarea || imagePreview) {
                pages.push({
                    content: contentTextarea?.value || '',
                    image: imagePreview?.src || this.settings.pages[index]?.image || ''
                });
            }
        });

        formData.pages = pages;
        this.settings = { ...this.settings, ...formData };
        
        // Save to localStorage
        localStorage.setItem('birthdaySettings', JSON.stringify(this.settings));
    }

    restart() {
        // Stop current animations
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        if (this.heartInterval) {
            clearInterval(this.heartInterval);
        }

        // Stop auto-flip
        this.stopAutoFlip();

        // Reset display
        const matrixRain = document.getElementById('matrix-rain');
        const contentDisplay = document.getElementById('contentDisplay');
        const giftImage = document.getElementById('gift-image');
        const bookContainer = document.querySelector('.book-container');
        const pageIndicator = document.getElementById('page-indicator');

        if (matrixRain) matrixRain.style.display = 'none';
        if (contentDisplay) contentDisplay.classList.remove('show');
        if (giftImage) giftImage.style.display = 'none';
        if (bookContainer) bookContainer.classList.remove('show');
        if (pageIndicator) pageIndicator.remove();

        // Reset book pages
        const pages = document.querySelectorAll('.page');
        pages.forEach(page => {
            page.classList.remove('flipped', 'flipping');
        });

        // Remove existing hearts
        document.querySelectorAll('.heart').forEach(heart => heart.remove());

        // Restart sequence
        setTimeout(() => {
            this.startCountdown();
        }, 1000);
    }

    setupBookEvents() {
        const book = document.getElementById('book');
        if (book) {
            book.addEventListener('click', (e) => {
                const page = e.target.closest('.page');
                if (page && !page.classList.contains('flipping')) {
                    this.flipPage(page);
                }
            });
        }
    }

    setupEnhancedBookInteraction() {
        const book = document.getElementById('book');
        if (!book) return;

        // Add keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (!book.classList.contains('show')) return;

            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                    e.preventDefault();
                    this.nextPage();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    this.previousPage();
                    break;
            }
        });

        // Add touch/swipe support
        this.setupTouchEvents(book);

        // Add page indicator
        this.createPageIndicator();

        // Auto-flip pages every 8 seconds
        this.startAutoFlip();
    }

    setupTouchEvents(book) {
        let startX = 0;
        let startY = 0;

        book.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });

        book.addEventListener('touchend', (e) => {
            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;

            const deltaX = endX - startX;
            const deltaY = endY - startY;

            // Check if it's a horizontal swipe (minimum 50px)
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
                if (deltaX > 0) {
                    this.previousPage(); // Swipe right = previous page
                } else {
                    this.nextPage(); // Swipe left = next page
                }
            }
        });
    }

    createPageIndicator() {
        // Remove existing indicator
        const existingIndicator = document.getElementById('page-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }

        const indicator = document.createElement('div');
        indicator.id = 'page-indicator';
        indicator.style.cssText = `
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            z-index: 1001;
            transition: opacity 0.3s ease;
            opacity: 0;
            font-family: Arial, sans-serif;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        `;
        document.body.appendChild(indicator);

        this.updatePageIndicator();
    }

    updatePageIndicator() {
        const indicator = document.getElementById('page-indicator');
        if (indicator) {
            const flippedPages = document.querySelectorAll('.page.flipped').length;
            indicator.textContent = `${flippedPages + 1} / ${this.totalPages}`;

            // Show indicator briefly
            indicator.style.opacity = '1';
            setTimeout(() => {
                indicator.style.opacity = '0';
            }, 2000);
        }
    }

    nextPage() {
        const pages = document.querySelectorAll('.page:not(.flipped)');
        if (pages.length > 0) {
            this.flipPage(pages[0]);
        }
    }

    previousPage() {
        const flippedPages = document.querySelectorAll('.page.flipped');
        if (flippedPages.length > 0) {
            const lastFlipped = flippedPages[flippedPages.length - 1];
            this.unflipPage(lastFlipped);
        }
    }

    flipPage(pageElement) {
        if (pageElement.classList.contains('flipping')) return;

        pageElement.classList.add('flipping');

        // Add subtle book shake effect
        const book = document.getElementById('book');
        if (book) {
            book.style.transform = 'translateX(2px)';
            setTimeout(() => book.style.transform = 'translateX(-1px)', 100);
            setTimeout(() => book.style.transform = 'translateX(0)', 200);
        }

        setTimeout(() => {
            pageElement.classList.remove('flipping');
            pageElement.classList.add('flipped');
            this.updatePageZIndex();
            this.updatePageIndicator();

            // Create page flip sound effect
            this.playFlipSound();
        }, 400);
    }

    unflipPage(pageElement) {
        if (pageElement.classList.contains('flipping')) return;

        pageElement.classList.add('flipping');

        setTimeout(() => {
            pageElement.classList.remove('flipping');
            pageElement.classList.remove('flipped');
            this.updatePageZIndex();
            this.updatePageIndicator();

            this.playFlipSound();
        }, 400);
    }

    updatePageZIndex() {
        const pages = document.querySelectorAll('.page');
        pages.forEach((page, index) => {
            if (page.classList.contains('flipped')) {
                page.style.zIndex = index + 1;
            } else {
                page.style.zIndex = this.totalPages - index;
            }
        });
    }

    playFlipSound() {
        // Create a subtle audio feedback for page flips
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.1);
        } catch (e) {
            // Ignore audio errors
        }
    }

    startAutoFlip() {
        // Auto-flip pages every 8 seconds
        this.autoFlipInterval = setInterval(() => {
            const unflippedPages = document.querySelectorAll('.page:not(.flipped)');
            if (unflippedPages.length > 0) {
                this.flipPage(unflippedPages[0]);
            } else {
                // Reset to first page
                const allPages = document.querySelectorAll('.page');
                allPages.forEach(page => {
                    page.classList.remove('flipped');
                });
                this.updatePageZIndex();
                this.updatePageIndicator();
            }
        }, 8000);
    }

    stopAutoFlip() {
        if (this.autoFlipInterval) {
            clearInterval(this.autoFlipInterval);
            this.autoFlipInterval = null;
        }
    }

    startCountdown() {
        const countdownTime = this.settings.countdownTime * 1000;

        setTimeout(() => {
            this.startSequence();
        }, countdownTime);
    }

    startSequence() {
        this.showMatrixRain();

        setTimeout(() => {
            this.showSequenceText();
        }, 2000);

        setTimeout(() => {
            this.showGiftImage();
        }, 5000);

        setTimeout(() => {
            this.showBook();
        }, 8000);

        if (this.settings.enableHeart) {
            this.startHeartEffect();
        }
    }

    showMatrixRain() {
        const canvas = document.getElementById('matrix-rain');
        canvas.style.display = 'block';
        this.startMatrixAnimation();
    }

    setupMatrixRain() {
        this.matrixCanvas = document.getElementById('matrix-rain');
        if (this.matrixCanvas) {
            this.matrixCtx = this.matrixCanvas.getContext('2d');
            this.resizeMatrix();
        }
    }

    resizeMatrix() {
        if (!this.matrixCanvas) return;

        this.matrixCanvas.width = window.innerWidth;
        this.matrixCanvas.height = window.innerHeight;
    }

    startMatrixAnimation() {
        const canvas = this.matrixCanvas;
        const ctx = this.matrixCtx;

        if (!canvas || !ctx) return;

        const columns = Math.floor(canvas.width / 20);
        const drops = Array(columns).fill(1);
        const chars = this.settings.matrixText.split('');

        const animate = () => {
            ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            ctx.font = '15px monospace';

            for (let i = 0; i < drops.length; i++) {
                const char = chars[Math.floor(Math.random() * chars.length)];
                ctx.fillStyle = Math.random() > 0.5 ? this.settings.matrixColor1 : this.settings.matrixColor2;
                ctx.fillText(char, i * 20, drops[i] * 20);

                if (drops[i] * 20 > canvas.height && Math.random() > 0.975) {
                    drops[i] = 0;
                }
                drops[i]++;
            }

            this.animationId = requestAnimationFrame(animate);
        };

        animate();
    }

    showSequenceText() {
        const contentDisplay = document.getElementById('contentDisplay');
        const contentText = document.getElementById('contentText');

        if (contentDisplay && contentText) {
            const sequences = this.settings.sequenceText.split('|');
            contentDisplay.classList.add('show');
            contentText.style.color = this.settings.sequenceColor;

            this.typewriterEffect(contentText, sequences.join(' '), () => {
                // Animation complete
            });
        }
    }

    typewriterEffect(element, text, callback) {
        element.innerHTML = '';
        let i = 0;

        const timer = setInterval(() => {
            if (i < text.length) {
                element.innerHTML += text.charAt(i);
                i++;
            } else {
                clearInterval(timer);
                if (callback) callback();
            }
        }, 100);
    }

    showGiftImage() {
        if (this.settings.giftImage) {
            const giftImg = document.getElementById('gift-image');
            if (giftImg) {
                giftImg.src = this.settings.giftImage;
                giftImg.style.display = 'block';
                giftImg.style.animation = 'giftCelebration 2s ease-in-out';
            }
        }
    }

    showBook() {
        console.log('📖 showBook called');
        console.log('📊 enableBook:', this.settings.enableBook);
        console.log('📄 pages count:', this.settings.pages?.length);

        if (this.settings.enableBook && this.settings.pages && this.settings.pages.length > 0) {
            console.log('✅ Book conditions met, creating pages...');
            this.createBookPages();

            const bookContainer = document.querySelector('.book-container');
            const book = document.getElementById('book');

            console.log('📚 Book container found:', !!bookContainer);
            console.log('📖 Book element found:', !!book);

            if (bookContainer && book) {
                bookContainer.classList.add('show');
                book.classList.add('show');
                this.isBookVisible = true;
                console.log('✅ Book is now visible!');
            } else {
                console.error('❌ Book container or book element not found!');
            }
        } else {
            console.log('❌ Book conditions not met');
        }
    }

    createBookPages() {
        const book = document.getElementById('book');
        if (!book) return;

        book.innerHTML = '';

        // If no pages are configured, create default pages with available images
        let pagesToCreate = this.settings.pages;
        if (!pagesToCreate || pagesToCreate.length === 0) {
            pagesToCreate = this.createDefaultPages();
        }

        console.log(`📚 Creating ${pagesToCreate.length} book pages...`);

        pagesToCreate.forEach((page, index) => {
            console.log(`📄 Creating page ${index + 1}:`, page);

            const pageElement = document.createElement('div');
            pageElement.className = 'page';
            pageElement.style.zIndex = pagesToCreate.length - index;
            pageElement.setAttribute('data-page-index', index);

            const frontSide = document.createElement('div');
            frontSide.className = 'page-front';

            const backSide = document.createElement('div');
            backSide.className = 'page-back';

            // Add content to front side
            if (page.image) {
                console.log(`📸 Loading image for page ${index + 1}:`, page.image);
                this.loadImageWithFallback(page.image, frontSide, index);

                // Add text overlay if content exists
                if (page.content) {
                    const textOverlay = document.createElement('div');
                    textOverlay.className = 'page-text-overlay';
                    textOverlay.innerHTML = page.content;
                    textOverlay.style.cssText = `
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        right: 0;
                        background: linear-gradient(transparent, rgba(0,0,0,0.7));
                        color: white;
                        padding: 20px 15px 15px;
                        font-size: 14px;
                        text-align: center;
                        border-radius: 0 0 8px 0;
                        text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
                    `;
                    frontSide.appendChild(textOverlay);
                }
            } else if (page.content) {
                // Text-only page
                const textDiv = document.createElement('div');
                textDiv.className = 'page-text-content';
                textDiv.innerHTML = page.content;
                textDiv.style.cssText = `
                    padding: 30px 20px;
                    font-size: 16px;
                    text-align: center;
                    color: #333;
                    background: linear-gradient(135deg, #fff 0%, #f8f8f8 100%);
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-sizing: border-box;
                    font-family: 'Georgia', serif;
                    line-height: 1.6;
                `;
                frontSide.appendChild(textDiv);
            }

            // Add page number
            const pageNumber = document.createElement('div');
            pageNumber.className = 'page-number';
            pageNumber.textContent = index + 1;
            pageNumber.style.cssText = `
                position: absolute;
                top: 10px;
                right: 15px;
                font-size: 12px;
                color: #666;
                background: rgba(255, 255, 255, 0.9);
                padding: 4px 8px;
                border-radius: 12px;
                z-index: 10;
                font-weight: bold;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            `;
            frontSide.appendChild(pageNumber);

            pageElement.appendChild(frontSide);
            pageElement.appendChild(backSide);
            book.appendChild(pageElement);
        });

        this.totalPages = pagesToCreate.length;
        this.setupEnhancedBookInteraction();
    }

    loadImageWithFallback(imageUrl, container, pageIndex) {
        // Try multiple methods to load the image
        const methods = [
            () => this.loadImageDirect(imageUrl, container, pageIndex),
            () => this.loadImageViaProxy(imageUrl, container, pageIndex),
            () => this.createImagePlaceholder(container, pageIndex)
        ];

        let currentMethod = 0;

        const tryNextMethod = () => {
            if (currentMethod < methods.length) {
                methods[currentMethod]()
                    .then(() => {
                        console.log(`✅ Image loaded successfully for page ${pageIndex + 1} using method ${currentMethod + 1}`);
                    })
                    .catch(() => {
                        console.warn(`⚠️ Method ${currentMethod + 1} failed for page ${pageIndex + 1}, trying next...`);
                        currentMethod++;
                        tryNextMethod();
                    });
            } else {
                console.error(`❌ All methods failed for page ${pageIndex + 1}`);
            }
        };

        tryNextMethod();
    }

    loadImageDirect(imageUrl, container, pageIndex) {
        return new Promise((resolve, reject) => {
            const img = document.createElement('img');
            img.style.cssText = `
                width: 100%;
                height: 100%;
                object-fit: cover;
                display: block;
                border-radius: inherit;
            `;

            img.onload = () => resolve();
            img.onerror = () => reject();

            // Set timeout for slow loading
            setTimeout(() => {
                if (!img.complete) {
                    reject(new Error('Timeout'));
                }
            }, 8000);

            img.src = imageUrl;
            img.alt = `Page ${pageIndex + 1}`;
            container.appendChild(img);
        });
    }

    loadImageViaProxy(imageUrl, container, pageIndex) {
        return new Promise((resolve, reject) => {
            // Try using a CORS proxy service
            const proxyUrl = `https://cors-anywhere.herokuapp.com/${imageUrl}`;
            const img = document.createElement('img');
            img.style.cssText = `
                width: 100%;
                height: 100%;
                object-fit: cover;
                display: block;
                border-radius: inherit;
            `;

            img.onload = () => resolve();
            img.onerror = () => reject();

            img.src = proxyUrl;
            img.alt = `Page ${pageIndex + 1}`;
            container.appendChild(img);
        });
    }

    createImagePlaceholder(container, pageIndex) {
        return new Promise((resolve) => {
            const placeholder = document.createElement('div');
            placeholder.style.cssText = `
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, #ff6b9d, #4ecdc4);
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 18px;
                text-align: center;
                border-radius: inherit;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
                flex-direction: column;
            `;

            placeholder.innerHTML = `
                <div style="font-size: 32px; margin-bottom: 10px;">🎂</div>
                <div>Birthday Page</div>
                <div style="font-size: 14px; opacity: 0.8;">${pageIndex + 1}</div>
            `;

            container.appendChild(placeholder);
            resolve();
        });
    }

    createDefaultPages() {
        // Create default pages using available images and content
        return [
            {
                image: './image/happy.gif',
                content: 'Happy Birthday! 🎉<br>Hope your special day is wonderful!'
            },
            {
                image: './gif/Cat Love GIF by KIKI.gif',
                content: 'Sending you lots of love! ❤️'
            },
            {
                content: `
                    <h2 style="color: #ff6b9d; margin-bottom: 20px;">🎂 Birthday Wishes</h2>
                    <p>May this year bring you happiness, success, and all the things you've been dreaming of!</p>
                    <p style="margin-top: 20px; font-style: italic;">With love and best wishes! 💕</p>
                `
            },
            {
                content: `
                    <h2 style="color: #4ecdc4; margin-bottom: 20px;">🌟 Special Memories</h2>
                    <p>Every moment with you is a treasure. Here's to creating many more beautiful memories together!</p>
                    <p style="margin-top: 20px; color: #666;">Click or swipe to turn pages →</p>
                `
            },
            {
                content: `
                    <h2 style="color: #ff9800; margin-bottom: 20px;">🎈 Final Page</h2>
                    <p>Thank you for being such an amazing person!</p>
                    <p style="margin-top: 20px; font-size: 24px;">🎉 THE END 🎉</p>
                `
            }
        ];
    }

    startHeartEffect() {
        this.heartInterval = setInterval(() => {
            this.createHeart();
        }, 1000);
    }

    createHeart() {
        const heart = document.createElement('div');
        heart.className = 'heart';
        heart.innerHTML = '❤️';
        heart.style.left = Math.random() * window.innerWidth + 'px';
        heart.style.top = window.innerHeight + 'px';

        document.body.appendChild(heart);

        setTimeout(() => {
            if (heart.parentNode) {
                heart.remove();
            }
        }, 4000);
    }

    setupStars() {
        const starsContainer = document.getElementById('starsContainer');
        if (!starsContainer) return;

        const numStars = 100;

        for (let i = 0; i < numStars; i++) {
            const star = document.createElement('div');
            star.className = 'star';

            // Random size
            const size = Math.random();
            if (size > 0.8) star.classList.add('large');
            else if (size > 0.5) star.classList.add('medium');
            else star.classList.add('small');

            // Random position
            star.style.left = Math.random() * 100 + '%';
            star.style.top = Math.random() * 100 + '%';

            // Random animation delay
            star.style.animationDelay = Math.random() * 2 + 's';

            starsContainer.appendChild(star);
        }

        starsContainer.style.display = 'block';
    }

    checkOrientation() {
        const orientationLock = document.getElementById('orientation-lock');
        if (!orientationLock) return;

        const isMobile = window.innerWidth <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

        if (isMobile && window.innerHeight < window.innerWidth) {
            // Landscape on mobile
            orientationLock.style.display = 'flex';
        } else {
            orientationLock.style.display = 'none';
        }
    }

    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.log('Fullscreen not supported:', err);
                alert(t('fullscreenNotSupported'));
            });
        } else {
            document.exitFullscreen();
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.birthdayWebsite = new BirthdayWebsite();
});
