@echo off
echo Website Cloning Commands
echo ========================

echo.
echo Method 1: Using wget (if installed)
echo wget --mirror --convert-links --adjust-extension --page-requisites --no-parent https://happybirthdaydeargift.vercel.app/?websiteId=************************

echo.
echo Method 2: Using curl to download specific files
echo curl -o index.html "https://happybirthdaydeargift.vercel.app/?websiteId=************************"

echo.
echo Method 3: Using PowerShell
echo Invoke-WebRequest -Uri "https://happybirthdaydeargift.vercel.app/?websiteId=************************" -OutFile "original.html"

echo.
echo Instructions:
echo 1. Copy any of the above commands
echo 2. Run in Command Prompt or PowerShell
echo 3. The website files will be downloaded

pause
