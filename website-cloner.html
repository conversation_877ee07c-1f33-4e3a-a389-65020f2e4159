<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Website Cloner Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        .method {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        .code {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
        .url-input {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 16px;
        }
        .iframe-container {
            margin: 20px 0;
            border-radius: 10px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 400px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 Website Cloner Tool</h1>
        
        <div class="method">
            <h3>📋 Method 1: Browser Console Script</h3>
            <p>1. Go to the website you want to clone</p>
            <p>2. Press F12 to open Developer Tools</p>
            <p>3. Go to Console tab</p>
            <p>4. Copy and paste this script:</p>
            <div class="code" id="consoleScript">
// Website Cloning Script
(function() {
    const html = document.documentElement.outerHTML;
    const blob = new Blob([html], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'cloned-website.html';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    console.log('Website HTML downloaded!');
})();
            </div>
            <button onclick="copyToClipboard('consoleScript')">📋 Copy Script</button>
        </div>

        <div class="method">
            <h3>🔗 Method 2: Direct URL Clone</h3>
            <p>Enter the website URL to preview and clone:</p>
            <input type="url" class="url-input" id="websiteUrl" 
                   placeholder="https://happybirthdaydeargift.vercel.app/?websiteId=************************"
                   value="https://happybirthdaydeargift.vercel.app/?websiteId=************************">
            <button onclick="loadWebsite()">🔍 Load Website</button>
            <button onclick="cloneFromProxy()">📥 Clone Website</button>
            
            <div class="iframe-container">
                <iframe id="websiteFrame" src="about:blank"></iframe>
            </div>
        </div>

        <div class="method">
            <h3>💻 Method 3: Command Line Tools</h3>
            <p>Use these commands in Command Prompt or PowerShell:</p>
            
            <h4>Using PowerShell:</h4>
            <div class="code" id="powershellCmd">
Invoke-WebRequest -Uri "https://happybirthdaydeargift.vercel.app/?websiteId=************************" -OutFile "birthday-website.html"
            </div>
            <button onclick="copyToClipboard('powershellCmd')">📋 Copy PowerShell Command</button>
            
            <h4>Using curl:</h4>
            <div class="code" id="curlCmd">
curl -o birthday-website.html "https://happybirthdaydeargift.vercel.app/?websiteId=************************"
            </div>
            <button onclick="copyToClipboard('curlCmd')">📋 Copy curl Command</button>
        </div>

        <div class="method">
            <h3>🛠️ Method 4: Advanced Cloning</h3>
            <p>For a complete clone with all assets:</p>
            <div class="code">
# Install wget first (if not available)
# Then run:
wget --mirror --convert-links --adjust-extension --page-requisites --no-parent --user-agent="Mozilla/5.0" "https://happybirthdaydeargift.vercel.app/?websiteId=************************"
            </div>
        </div>
    </div>

    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            navigator.clipboard.writeText(text).then(() => {
                alert('📋 Copied to clipboard!');
            });
        }

        function loadWebsite() {
            const url = document.getElementById('websiteUrl').value;
            const iframe = document.getElementById('websiteFrame');
            
            // Use a CORS proxy to load the website
            const proxyUrl = `https://api.allorigins.win/raw?url=${encodeURIComponent(url)}`;
            iframe.src = proxyUrl;
        }

        async function cloneFromProxy() {
            const url = document.getElementById('websiteUrl').value;
            
            try {
                // Use CORS proxy to fetch the website
                const proxyUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(url)}`;
                const response = await fetch(proxyUrl);
                const data = await response.json();
                
                // Download the HTML
                const blob = new Blob([data.contents], { type: 'text/html' });
                const downloadUrl = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = downloadUrl;
                a.download = 'cloned-website.html';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(downloadUrl);
                
                alert('✅ Website cloned successfully!');
            } catch (error) {
                alert('❌ Error cloning website: ' + error.message);
                console.error('Clone error:', error);
            }
        }

        // Auto-load the birthday website
        window.onload = function() {
            loadWebsite();
        };
    </script>
</body>
</html>
